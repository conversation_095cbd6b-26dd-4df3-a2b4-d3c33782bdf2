
import { useState, useEffect } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON>ontent, 
  <PERSON><PERSON><PERSON>er, 
  Card<PERSON>ead<PERSON>, 
  Card<PERSON>itle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useToast } from "@/hooks/use-toast";
import { X, ShoppingCart, AlertCircle, Search, Plus } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import type { Product, SaleItem } from '@/types/firebase';
import { getFirestore, collection, getDocs, doc, getDoc, updateDoc, addDoc, serverTimestamp } from "firebase/firestore";

interface RecordSaleFormProps {
  onClose: () => void;
  onSaleRecorded: () => void;
}

export function RecordSaleForm({ onClose, onSaleRecorded }: RecordSaleFormProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedProduct, setSelectedProduct] = useState("");
  const [productQuantity, setProductQuantity] = useState(1);
  const [customerName, setCustomerName] = useState("");
  const [paymentMethod, setPaymentMethod] = useState("cash");
  const [saleItems, setSaleItems] = useState<SaleItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const { toast } = useToast();

  const { data: products = [], isLoading: isLoadingProducts } = useQuery<Product[]>({
    queryKey: ["products"],
    queryFn: async () => {
      const db = getFirestore();
      const productsCollection = collection(db, "products");
      const querySnapshot = await getDocs(productsCollection);
      
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<Product, 'id'>)
      }));
    }
  });

  const filteredProducts = products.filter(product => 
    product.name?.toLowerCase().includes(searchTerm.toLowerCase()) && 
    product.stock > 0
  );

  const totalAmount = saleItems.reduce(
    (sum, item) => sum + item.price * item.quantity, 
    0
  );

  const handleProductSelect = (productId: string) => {
    setSelectedProduct(productId);
    setSearchTerm(products.find(p => p.id === productId)?.name || "");
    setShowResults(false);
  };

  const handleSearchFocus = () => {
    setShowResults(true);
  };

  const handleSearchBlur = () => {
    // Small delay to allow clicking on search results
    setTimeout(() => setShowResults(false), 200);
  };

  const handleAddItem = () => {
    if (!selectedProduct || productQuantity <= 0) {
      toast({
        title: "Error",
        description: "Please select a product and valid quantity",
        variant: "destructive",
      });
      return;
    }

    const product = products.find((p) => p.id === selectedProduct);
    
    if (!product) {
      toast({
        title: "Error",
        description: "Product not found",
        variant: "destructive",
      });
      return;
    }

    if (product.stock < productQuantity) {
      toast({
        title: "Insufficient Stock",
        description: `Only ${product.stock} items available`,
        variant: "destructive",
      });
      return;
    }

    const existingItemIndex = saleItems.findIndex(
      item => item.productId === selectedProduct
    );

    if (existingItemIndex >= 0) {
      const updatedItems = [...saleItems];
      updatedItems[existingItemIndex].quantity += productQuantity;
      setSaleItems(updatedItems);
    } else {
      setSaleItems([
        ...saleItems,
        {
          productId: product.id,
          productName: product.name,
          quantity: productQuantity,
          price: product.sellingPrice || product.price // Use sellingPrice if available, fallback to legacy price
        }
      ]);
    }

    setSelectedProduct("");
    setSearchTerm("");
    setProductQuantity(1);
  };

  const handleRemoveItem = (index: number) => {
    setSaleItems(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (saleItems.length === 0) {
      toast({
        title: "Error",
        description: "Please add at least one product to the sale",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);
      
      const saleData = {
        date: new Date().toISOString().split('T')[0],
        items: saleItems.reduce((sum, item) => sum + item.quantity, 0),
        amount: totalAmount,
        customer: customerName || "Walk-in Customer",
        paymentMethod,
        status: "completed",
        products: saleItems,
        timestamp: new Date().toISOString()
      };

      // Use native Firebase SDK for more reliable operation
      const db = getFirestore();
      const salesCollection = collection(db, "sales");
      await addDoc(salesCollection, saleData);

      // Update product stock
      for (const item of saleItems) {
        const productRef = doc(db, "products", item.productId);
        const productSnap = await getDoc(productRef);
        
        if (productSnap.exists()) {
          const productData = productSnap.data();
          const currentStock = productData.stock || 0;
          
          await updateDoc(productRef, {
            stock: Math.max(0, currentStock - item.quantity)
          });
        }
      }
      
      toast({
        title: "Success",
        description: "Sale recorded successfully",
      });
      
      onSaleRecorded();
      onClose();
    } catch (error: any) {
      console.error("Sale recording error:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to record sale",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="w-full border-0 shadow-none">
      <CardHeader className="space-y-0 pb-2">
        <CardTitle>Record New Sale</CardTitle>
      </CardHeader>
      <form onSubmit={handleSubmit}>
        <CardContent className="space-y-4 pt-4">
          <div className="space-y-2">
            <Label htmlFor="customer">Customer Name</Label>
            <Input
              id="customer"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              placeholder="Walk-in Customer"
            />
          </div>

          <div className="border rounded-md p-3 space-y-3">
            <div className="grid grid-cols-12 gap-2">
              <div className="col-span-7 space-y-1">
                <Label htmlFor="product">Product</Label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                    <Search className="h-4 w-4 text-muted-foreground" />
                  </div>
                  <Input
                    id="product"
                    type="text"
                    placeholder="Search stationery items..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    onFocus={handleSearchFocus}
                    onBlur={handleSearchBlur}
                    className="pl-10"
                  />
                  {showResults && filteredProducts.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-background border rounded-md shadow-lg max-h-48 overflow-y-auto">
                      {filteredProducts.map(product => (
                        <div
                          key={product.id}
                          className="p-2 hover:bg-muted cursor-pointer"
                          onClick={() => handleProductSelect(product.id)}
                        >
                          <div className="font-medium">{product.name}</div>
                          <div className="text-xs text-muted-foreground flex justify-between">
                            <span>Tsh{(product.sellingPrice || product.price)?.toFixed(2)}</span>
                            <span>Stock: {product.stock}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div className="col-span-2 space-y-1">
                <Label htmlFor="quantity">Qty</Label>
                <Input
                  id="quantity"
                  type="number"
                  min="1"
                  value={productQuantity}
                  onChange={(e) => setProductQuantity(parseInt(e.target.value) || 1)}
                />
              </div>
              <div className="col-span-3 flex items-end">
                <Button 
                  type="button" 
                  onClick={handleAddItem}
                  variant="outline"
                  className="w-full"
                  disabled={!selectedProduct}
                >
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {saleItems.length > 0 ? (
            <div className="border rounded-md divide-y">
              {saleItems.map((item, index) => (
                <div key={index} className="flex items-center justify-between p-3">
                  <div>
                    <div className="font-medium">{item.productName}</div>
                    <div className="text-sm text-muted-foreground">
                      {item.quantity} x ${item.price?.toFixed(2)}
                    </div>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="font-medium">
                      ${(item.price * item.quantity).toFixed(2)}
                    </div>
                    <Button 
                      type="button" 
                      variant="ghost" 
                      size="sm"
                      onClick={() => handleRemoveItem(index)}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
              <div className="flex items-center justify-between p-3 bg-muted/50">
                <div className="font-medium">Total</div>
                <div className="text-lg font-bold">${totalAmount.toFixed(2)}</div>
              </div>
            </div>
          ) : (
            <div className="border rounded-md p-6 flex flex-col items-center justify-center text-center text-muted-foreground">
              <ShoppingCart className="h-10 w-10 mb-2" />
              <p>No items added yet</p>
              <p className="text-sm">Search and select stationery items to add to this sale</p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="paymentMethod">Payment Method</Label>
            <Select value={paymentMethod} onValueChange={setPaymentMethod}>
              <SelectTrigger>
                <SelectValue placeholder="Select payment method" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="cash">Cash</SelectItem>
                <SelectItem value="card">Credit/Debit Card</SelectItem>
                <SelectItem value="mobile">Mobile Payment</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
        <CardFooter className="flex justify-end space-x-2">
          <Button variant="outline" type="button" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={isLoading || saleItems.length === 0}
          >
            {isLoading ? (
              <>
                <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                Processing...
              </>
            ) : (
              "Complete Sale"
            )}
          </Button>
        </CardFooter>
      </form>
    </Card>
  );
}
