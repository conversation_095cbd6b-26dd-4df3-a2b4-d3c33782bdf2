
import { Outlet, Navigate } from 'react-router-dom';
import { Sidebar, SidebarProvider } from './Sidebar';
import { Navbar } from './Navbar';
import { useAuth } from '@/context/AuthContext';
import { useIsMobile } from '@/hooks/use-mobile';

export function Layout() {
  const { currentUser, isLoading } = useAuth();
  const isMobile = useIsMobile();

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="space-y-4 text-center">
          <div className="h-10 w-10 animate-spin rounded-full border-4 border-primary border-t-transparent mx-auto"></div>
          <p className="text-lg font-medium">Loading...</p>
        </div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full overflow-hidden bg-background layout-container">
        <Sidebar />
        <div className="flex flex-1 flex-col overflow-hidden min-w-0 content-wrapper">
          <Navbar />
          <main className="flex-1 overflow-y-auto p-3 md:p-4 lg:p-6 animate-fade-in main-content">
            <div className="w-full max-w-none">
              <Outlet />
            </div>
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
}
