
import { useState } from "react";
import { 
  Calendar,
  CreditCard,
  DollarSign,
  ShoppingCart,
  Package,
  Users,
  TrendingUp,
  Filter,
  Download,
  Search
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { MetricCard } from "@/components/common/MetricCard";
import { DataTable } from "@/components/common/DataTable";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Tabs,
  TabsContent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs";
import { 
  LineChart, 
  Line, 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer 
} from "recharts";

// Mock data for sales
const recentSales = [
  { id: "1", date: "2023-06-05", product: "Smartphone X", amount: 1299.99, customer: "<PERSON>", payment: "Credit Card", status: "Completed" },
  { id: "2", date: "2023-06-05", product: "Wireless Headphones", amount: 199.99, customer: "<PERSON>", payment: "PayPal", status: "Completed" },
  { id: "3", date: "2023-06-04", product: "Laptop Pro", amount: 2499.99, customer: "Bob Martin", payment: "Credit Card", status: "Completed" },
  { id: "4", date: "2023-06-04", product: "Smart Watch", amount: 399.99, customer: "Carol Davis", payment: "Debit Card", status: "Pending" },
  { id: "5", date: "2023-06-03", product: "Gaming Console", amount: 499.99, customer: "David Wilson", payment: "Cash", status: "Completed" },
  { id: "6", date: "2023-06-03", product: "Bluetooth Speaker", amount: 129.99, customer: "Eve Brown", payment: "Credit Card", status: "Refunded" },
  { id: "7", date: "2023-06-02", product: "Tablet Pro", amount: 899.99, customer: "Frank Miller", payment: "Debit Card", status: "Completed" },
  { id: "8", date: "2023-06-02", product: "Wireless Keyboard", amount: 79.99, customer: "Grace Lee", payment: "PayPal", status: "Completed" },
  { id: "9", date: "2023-06-01", product: "Noise Cancelling Headphones", amount: 349.99, customer: "Henry Wilson", payment: "Credit Card", status: "Pending" },
  { id: "10", date: "2023-06-01", product: "Smartphone Case", amount: 29.99, customer: "Irene Garcia", payment: "Cash", status: "Completed" },
];

// Mock data for daily sales chart
const dailySalesData = [
  { day: "Monday", sales: 3200 },
  { day: "Tuesday", sales: 4500 },
  { day: "Wednesday", sales: 3800 },
  { day: "Thursday", sales: 5100 },
  { day: "Friday", sales: 6000 },
  { day: "Saturday", sales: 4300 },
  { day: "Sunday", sales: 3100 },
];

// Mock data for monthly sales chart
const monthlySalesData = [
  { month: "Jan", sales: 25000 },
  { month: "Feb", sales: 28000 },
  { month: "Mar", sales: 32000 },
  { month: "Apr", sales: 30000 },
  { month: "May", sales: 35000 },
  { month: "Jun", sales: 37000 },
  { month: "Jul", sales: 33000 },
  { month: "Aug", sales: 36000 },
  { month: "Sep", sales: 39000 },
  { month: "Oct", sales: 41000 },
  { month: "Nov", sales: 45000 },
  { month: "Dec", sales: 50000 },
];

export default function SalesPage() {
  const [periodFilter, setPeriodFilter] = useState("weekly");
  const [searchQuery, setSearchQuery] = useState("");

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Sales Management</h1>
        <p className="text-muted-foreground">
          Monitor your sales performance and transaction history.
        </p>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Today's Sales"
          value="Tsh 3,429.99"
          description="15 transactions"
          icon={<ShoppingCart className="h-4 w-4" />}
          trend={{ value: 12, positive: true }}
        />
        <MetricCard
          title="This Week"
          value="Tsh 25,990.50"
          description="103 transactions"
          icon={<CreditCard className="h-4 w-4" />}
          trend={{ value: 8, positive: true }}
        />
        <MetricCard
          title="This Month"
          value="Tsh 103,270.25"
          description="428 transactions"
          icon={<DollarSign className="h-4 w-4" />}
          trend={{ value: 5, positive: true }}
        />
        <MetricCard
          title="Revenue Target"
          value="87%"
          description="Monthly goal"
          icon={<TrendingUp className="h-4 w-4" />}
        />
      </div>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between pb-2">
          <div>
            <CardTitle>Sales Overview</CardTitle>
            <CardDescription>Track your sales performance over time</CardDescription>
          </div>
          <div className="flex items-center space-x-2">
            <Select value={periodFilter} onValueChange={setPeriodFilter}>
              <SelectTrigger className="w-[160px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="daily">Daily</SelectItem>
                <SelectItem value="weekly">Weekly</SelectItem>
                <SelectItem value="monthly">Monthly</SelectItem>
                <SelectItem value="yearly">Yearly</SelectItem>
              </SelectContent>
            </Select>
            <Button variant="outline" size="icon">
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="bar" className="w-full">
            <TabsList className="w-full mb-4 max-w-md mx-auto grid grid-cols-2">
              <TabsTrigger value="bar">Bar Chart</TabsTrigger>
              <TabsTrigger value="line">Line Chart</TabsTrigger>
            </TabsList>
            <TabsContent value="bar" className="w-full">
              <div className="h-[350px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={periodFilter === "monthly" ? monthlySalesData : dailySalesData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey={periodFilter === "monthly" ? "month" : "day"} />
                    <YAxis />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'white',
                        borderRadius: '8px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                        border: 'none',
                      }}
                      formatter={(value) => [`Tsh ${value}`, 'Sales']}
                    />
                    <Legend />
                    <Bar dataKey="sales" name="Sales" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>
            <TabsContent value="line" className="w-full">
              <div className="h-[350px] w-full">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart
                    data={periodFilter === "monthly" ? monthlySalesData : dailySalesData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                    <XAxis dataKey={periodFilter === "monthly" ? "month" : "day"} />
                    <YAxis />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'white',
                        borderRadius: '8px',
                        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                        border: 'none',
                      }}
                      formatter={(value) => [`Tsh ${value}`, 'Sales']}
                    />
                    <Legend />
                    <Line type="monotone" dataKey="sales" name="Sales" stroke="hsl(var(--primary))" strokeWidth={2} dot={{ r: 4 }} />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <h2 className="text-xl font-semibold">Recent Transactions</h2>
          <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
            <div className="relative w-full sm:w-64">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search transactions..."
                className="pl-8 w-full"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <Button variant="outline" size="icon" className="hidden sm:flex">
              <Filter className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="icon" className="hidden sm:flex">
              <Calendar className="h-4 w-4" />
            </Button>
          </div>
        </div>

        <DataTable
          data={recentSales.filter(sale => 
            sale.product.toLowerCase().includes(searchQuery.toLowerCase()) ||
            sale.customer.toLowerCase().includes(searchQuery.toLowerCase()) ||
            sale.status.toLowerCase().includes(searchQuery.toLowerCase())
          )}
          columns={[
            {
              id: "date",
              header: "Date",
              cell: (row) => <div>{row.date}</div>,
            },
            {
              id: "product",
              header: "Product",
              cell: (row) => <div className="font-medium">{row.product}</div>,
            },
            {
              id: "customer",
              header: "Customer",
              cell: (row) => <div>{row.customer}</div>,
            },
            {
              id: "payment",
              header: "Payment",
              cell: (row) => <div>{row.payment}</div>,
            },
            {
              id: "status",
              header: "Status",
              cell: (row) => {
                let statusColor = "";
                switch (row.status) {
                  case "Completed":
                    statusColor = "bg-green-100 text-green-700 border-green-200";
                    break;
                  case "Pending":
                    statusColor = "bg-yellow-100 text-yellow-700 border-yellow-200";
                    break;
                  case "Refunded":
                    statusColor = "bg-red-100 text-red-700 border-red-200";
                    break;
                  default:
                    statusColor = "bg-gray-100 text-gray-700 border-gray-200";
                }
                return (
                  <div className={`inline-block px-2 py-1 rounded text-xs font-medium ${statusColor}`}>
                    {row.status}
                  </div>
                );
              },
            },
            {
              id: "amount",
              header: "Amount",
              cell: (row) => (
                <div className="font-medium text-right">
                  ${row.amount.toFixed(2)}
                </div>
              ),
            },
          ]}
        />
      </div>
    </div>
  );
}
