import { useState } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  Ta<PERSON>, 
  <PERSON><PERSON>Content, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/AuthContext";
import {
  ShoppingCart,
  Package,
  Users,
  DollarSign,
  Calendar,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Filter,
  Download
} from "lucide-react";
import { getFirestore, collection, getDocs, query, orderBy, limit, where, Timestamp } from "firebase/firestore";
import { DataTable } from "@/components/common/DataTable";

interface Activity {
  id: string;
  type: 'sale' | 'inventory' | 'user' | 'expense' | 'system';
  title: string;
  description: string;
  timestamp: Date;
  user?: string;
  amount?: number;
  status?: 'success' | 'warning' | 'error' | 'info';
  metadata?: any;
}

export default function Activities() {
  const { isAdmin, currentUser } = useAuth();
  const [activeTab, setActiveTab] = useState("all");

  // Fetch activities data
  const { data: activitiesData = [], isLoading: isLoadingActivities } = useQuery<Activity[]>({
    queryKey: ["activities"],
    queryFn: async () => {
      const db = getFirestore();
      const activities: Activity[] = [];

      try {
        // Fetch sales activities
        const salesRef = collection(db, 'sales');
        const salesQuery = query(salesRef, orderBy('date', 'desc'), limit(50));
        const salesSnapshot = await getDocs(salesQuery);
        
        salesSnapshot.docs.forEach(doc => {
          const data = doc.data();
          activities.push({
            id: `sale-${doc.id}`,
            type: 'sale',
            title: 'Sale Completed',
            description: `Sale of ${data.product?.name || 'Unknown Product'} - Tsh ${data.amount?.toFixed(2) || '0.00'}`,
            timestamp: data.date ? new Date(data.date) : new Date(),
            user: data.user || 'Unknown User',
            amount: data.amount || 0,
            status: 'success',
            metadata: data
          });
        });

        // Fetch inventory activities (low stock items)
        const productsRef = collection(db, 'products');
        const productsSnapshot = await getDocs(productsRef);
        
        productsSnapshot.docs.forEach(doc => {
          const data = doc.data();
          if (data.stock <= (data.lowStockThreshold || 10)) {
            activities.push({
              id: `inventory-${doc.id}`,
              type: 'inventory',
              title: 'Low Stock Alert',
              description: `${data.name} is running low - ${data.stock} remaining`,
              timestamp: new Date(),
              status: 'warning',
              metadata: data
            });
          }
        });

        // Fetch expense activities
        const expensesRef = collection(db, 'expenses');
        const expensesQuery = query(expensesRef, orderBy('date', 'desc'), limit(30));
        const expensesSnapshot = await getDocs(expensesQuery);
        
        expensesSnapshot.docs.forEach(doc => {
          const data = doc.data();
          activities.push({
            id: `expense-${doc.id}`,
            type: 'expense',
            title: 'Expense Recorded',
            description: `${data.title} - Tsh ${data.amount?.toFixed(2) || '0.00'}`,
            timestamp: data.date ? new Date(data.date) : new Date(),
            user: data.user || 'Unknown User',
            amount: data.amount || 0,
            status: data.status === 'approved' ? 'success' : data.status === 'pending' ? 'info' : 'warning',
            metadata: data
          });
        });

        // Sort all activities by timestamp
        return activities.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
      } catch (error) {
        console.error('Error fetching activities:', error);
        return [];
      }
    }
  });

  // Filter activities based on active tab
  const filteredActivities = activitiesData.filter(activity => {
    if (activeTab === "all") return true;
    return activity.type === activeTab;
  });

  // Activity columns for DataTable
  const activityColumns = [
    {
      id: "type",
      header: "Type",
      cell: (row: Activity) => {
        const getIcon = () => {
          switch (row.type) {
            case 'sale': return <ShoppingCart className="h-4 w-4 text-green-500" />;
            case 'inventory': return <Package className="h-4 w-4 text-amber-500" />;
            case 'expense': return <DollarSign className="h-4 w-4 text-blue-500" />;
            case 'user': return <Users className="h-4 w-4 text-purple-500" />;
            default: return <Calendar className="h-4 w-4 text-gray-500" />;
          }
        };
        
        return (
          <div className="flex items-center gap-2">
            {getIcon()}
            <span className="capitalize">{row.type}</span>
          </div>
        );
      },
    },
    {
      id: "title",
      header: "Activity",
      cell: (row: Activity) => (
        <div>
          <div className="font-medium">{row.title}</div>
          <div className="text-sm text-muted-foreground">{row.description}</div>
        </div>
      ),
    },
    {
      id: "timestamp",
      header: "Time",
      cell: (row: Activity) => (
        <div className="text-sm">
          <div>{row.timestamp.toLocaleDateString()}</div>
          <div className="text-muted-foreground">{row.timestamp.toLocaleTimeString()}</div>
        </div>
      ),
    },
    {
      id: "user",
      header: "User",
      cell: (row: Activity) => row.user || '-',
    },
    {
      id: "amount",
      header: "Amount",
      cell: (row: Activity) => row.amount ? `Tsh ${row.amount.toFixed(2)}` : '-',
    },
    {
      id: "status",
      header: "Status",
      cell: (row: Activity) => {
        if (!row.status) return '-';
        
        const getStatusColor = () => {
          switch (row.status) {
            case 'success': return 'bg-green-100 text-green-800';
            case 'warning': return 'bg-amber-100 text-amber-800';
            case 'error': return 'bg-red-100 text-red-800';
            case 'info': return 'bg-blue-100 text-blue-800';
            default: return 'bg-gray-100 text-gray-800';
          }
        };
        
        return (
          <Badge className={`${getStatusColor()} capitalize`}>
            {row.status}
          </Badge>
        );
      },
    },
  ];

  // Activity summary metrics
  const totalActivities = activitiesData.length;
  const todayActivities = activitiesData.filter(activity => {
    const today = new Date();
    const activityDate = new Date(activity.timestamp);
    return activityDate.toDateString() === today.toDateString();
  }).length;
  
  const salesActivities = activitiesData.filter(activity => activity.type === 'sale').length;
  const warningActivities = activitiesData.filter(activity => activity.status === 'warning').length;

  return (
    <div className="space-y-4 sm:space-y-6 animate-fade-in">
      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Activity Log</h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Track all activities and events in your stationery shop.
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="mr-2 h-4 w-4" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Activity Summary Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Activities</CardTitle>
            <Calendar className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalActivities}</div>
            <p className="text-xs text-muted-foreground">All time</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Activities</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{todayActivities}</div>
            <p className="text-xs text-muted-foreground">Last 24 hours</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Sales Activities</CardTitle>
            <ShoppingCart className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{salesActivities}</div>
            <p className="text-xs text-muted-foreground">Total sales recorded</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Warnings</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{warningActivities}</div>
            <p className="text-xs text-muted-foreground">Require attention</p>
          </CardContent>
        </Card>
      </div>

      {/* Activities Table */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2 sm:grid-cols-5">
          <TabsTrigger value="all">All</TabsTrigger>
          <TabsTrigger value="sale">Sales</TabsTrigger>
          <TabsTrigger value="inventory">Inventory</TabsTrigger>
          <TabsTrigger value="expense">Expenses</TabsTrigger>
          <TabsTrigger value="user">Users</TabsTrigger>
        </TabsList>
        
        <TabsContent value={activeTab} className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">
                {activeTab === "all" ? "All Activities" : `${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Activities`}
              </CardTitle>
              <CardDescription>
                {activeTab === "all" 
                  ? "Complete activity log for your stationery shop" 
                  : `${activeTab.charAt(0).toUpperCase() + activeTab.slice(1)}-related activities and events`
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <DataTable
                data={filteredActivities}
                columns={activityColumns}
                isLoading={isLoadingActivities}
                emptyMessage={`No ${activeTab === "all" ? "" : activeTab} activities found`}
              />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
