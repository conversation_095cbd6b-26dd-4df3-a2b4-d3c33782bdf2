import { useState, useEffect } from "react";
import {
  MoreHorizontal,
  Plus,
  Search,
  UserPlus,
  Users,
  Eye,
  Edit,
  UserX,
  Trash2,
  Check,
  X
} from "lucide-react";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DataTable } from "@/components/common/DataTable";
import { useAuth } from "@/context/AuthContext";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogFooter,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { createUserWithEmailAndPassword, getAuth } from "firebase/auth";
import { firestore, firebaseFirestore } from "@/lib/firebase";
import { doc, setDoc, updateDoc, deleteDoc } from "firebase/firestore";
import type { FirestoreUser } from "@/types/firebase";

type Role = "admin" | "shopkeeper";
const roleOptions: Role[] = ["admin", "shopkeeper"];

interface StaffMember {
  id: string;
  name: string;
  role: Role;
  email: string | null;
  status: string;
}

const useStaffData = () => {
  const [staffData, setStaffData] = useState<StaffMember[]>([]);

  useEffect(() => {
    const unsubscribe = firestore.collection("users").onSnapshot((users) => {
      const userData = users.map(user => ({
        id: user.id,
        name: user.displayName || user.email?.split("@")[0] || "Unknown",
        role: (user.role || "shopkeeper") as Role,
        email: user.email,
        status: user.status || "active"
      }));
      setStaffData(userData);
    });

    return () => unsubscribe();
  }, []);

  return staffData;
};

interface NewStaffForm {
  name: string;
  email: string;
  role: Role | "";
  password: string;
}

export default function Staff() {
  const { isAdmin } = useAuth();
  const { toast } = useToast();
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddStaffOpen, setIsAddStaffOpen] = useState(false);
  const [newStaff, setNewStaff] = useState<NewStaffForm>({
    name: "",
    email: "",
    role: "",
    password: "",
  });

  // State for user management operations
  const [viewingUser, setViewingUser] = useState<StaffMember | null>(null);
  const [editingUser, setEditingUser] = useState<StaffMember | null>(null);
  const [editForm, setEditForm] = useState({ name: "", email: "", role: "" as Role | "" });
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeactivating, setIsDeactivating] = useState<string | null>(null);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);

  const staffData = useStaffData();
  
  // Metrics calculations
  const totalStaff = staffData.length;
  const activeStaff = staffData.filter(staff => staff.status === 'active').length;

  const handleAddStaff = async () => {
    try {
      // Validate form
      if (!newStaff.name || !newStaff.email || !newStaff.role || !newStaff.password) {
        toast({
          title: "Missing Information",
          description: "Please fill in all fields to add a new staff member.",
          variant: "destructive",
        });
        return;
      }

      // Create Firebase Auth user
      const auth = getAuth();
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        newStaff.email,
        newStaff.password
      );

      // Create Firestore user document
      await setDoc(doc(firebaseFirestore, "users", userCredential.user.uid), {
        email: newStaff.email,
        displayName: newStaff.name,
        role: newStaff.role,
        status: "active",
        createdAt: new Date(),
      });

      toast({
        title: "Staff Added",
        description: `${newStaff.name} has been added as ${newStaff.role}.`,
      });

      // Reset form and close dialog
      setNewStaff({
        name: "",
        email: "",
        role: "",
        password: "",
      });
      setIsAddStaffOpen(false);
    } catch (error) {
      console.error("Error adding staff:", error);
      toast({
        title: "Error",
        description: "Failed to add staff member. Please try again.",
        variant: "destructive",
      });
    }
  };

  // User management CRUD operations
  const handleViewUser = (user: StaffMember) => {
    setViewingUser(user);
  };

  const handleEditUser = (user: StaffMember) => {
    setEditingUser(user);
    setEditForm({
      name: user.name,
      email: user.email || "",
      role: user.role
    });
  };

  const handleUpdateUser = async () => {
    if (!editingUser || !editForm.name.trim() || !editForm.email.trim() || !editForm.role) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsUpdating(true);
      const userRef = doc(firebaseFirestore, "users", editingUser.id);

      await updateDoc(userRef, {
        displayName: editForm.name,
        email: editForm.email,
        role: editForm.role,
        updatedAt: new Date(),
      });

      toast({
        title: "Success",
        description: "User updated successfully",
      });

      setEditingUser(null);
      setEditForm({ name: "", email: "", role: "" });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update user",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleToggleUserStatus = async (userId: string, currentStatus: string) => {
    try {
      setIsDeactivating(userId);
      const userRef = doc(firebaseFirestore, "users", userId);
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';

      await updateDoc(userRef, {
        status: newStatus,
        [newStatus === 'inactive' ? 'deactivatedAt' : 'reactivatedAt']: new Date(),
      });

      toast({
        title: "Success",
        description: `User ${newStatus === 'inactive' ? 'deactivated' : 'activated'} successfully`,
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || `Failed to ${currentStatus === 'active' ? 'deactivate' : 'activate'} user`,
        variant: "destructive",
      });
    } finally {
      setIsDeactivating(null);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      setIsDeleting(userId);
      const userRef = doc(firebaseFirestore, "users", userId);

      await deleteDoc(userRef);

      toast({
        title: "Success",
        description: "User deleted successfully",
      });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete user",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(null);
    }
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Store Staff Management</h1>
        <p className="text-muted-foreground">
          Monitor and manage your stationery shop staff and sales associates.
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Staff</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalStaff}</div>
            <p className="text-xs text-muted-foreground">
              {activeStaff} currently active
            </p>
          </CardContent>
        </Card>
      </div>

      <div className="space-y-4">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="text"
              placeholder="Search staff..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          
          {isAdmin() && (
            <Button onClick={() => setIsAddStaffOpen(true)}>
              <Plus className="mr-2 h-4 w-4" />
              Add Staff
            </Button>
          )}
        </div>

        <DataTable
          data={staffData}
          columns={[
            {
              id: "name",
              header: "Name",
              cell: (row) => <div className="font-medium">{row.name}</div>,
            },
            {
              id: "role",
              header: "Role",
              cell: (row) => <div>{row.role}</div>,
            },
            {
              id: "email",
              header: "Email",
              cell: (row) => <div>{row.email}</div>,
            },
            {
              id: "status",
              header: "Status",
              cell: (row) => (
                <div className={`px-2 py-1 rounded-full text-xs font-medium inline-block ${
                  row.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                }`}>
                  {row.status.charAt(0).toUpperCase() + row.status.slice(1)}
                </div>
              ),
            },
            ...(isAdmin() ? [{
              id: "actions",
              header: "",
              cell: (row) => (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => handleViewUser(row)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View Details
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => handleEditUser(row)}>
                      <Edit className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    {row.status === 'active' ? (
                      <DropdownMenuItem
                        onClick={() => handleToggleUserStatus(row.id, row.status)}
                        className="text-amber-600"
                        disabled={isDeactivating === row.id}
                      >
                        <UserX className="mr-2 h-4 w-4" />
                        {isDeactivating === row.id ? "Deactivating..." : "Deactivate"}
                      </DropdownMenuItem>
                    ) : (
                      <DropdownMenuItem
                        onClick={() => handleToggleUserStatus(row.id, row.status)}
                        className="text-green-600"
                        disabled={isDeactivating === row.id}
                      >
                        <Check className="mr-2 h-4 w-4" />
                        {isDeactivating === row.id ? "Activating..." : "Activate"}
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem
                      onClick={() => handleDeleteUser(row.id)}
                      className="text-red-600"
                      disabled={isDeleting === row.id}
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      {isDeleting === row.id ? "Deleting..." : "Delete"}
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ),
            }] : []),
          ]}
        />
      </div>

      <Dialog open={isAddStaffOpen} onOpenChange={setIsAddStaffOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Staff Member</DialogTitle>
            <DialogDescription>
              Create a new authenticated account for staff member.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={newStaff.name}
                onChange={(e) => setNewStaff({ ...newStaff, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="email" className="text-right">
                Email
              </Label>
              <Input
                id="email"
                type="email"
                value={newStaff.email}
                onChange={(e) => setNewStaff({ ...newStaff, email: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="role" className="text-right">
                Role
              </Label>
              <div className="col-span-3">
                <select
                  id="role"
                  value={newStaff.role}
                  onChange={(e) => setNewStaff({ ...newStaff, role: e.target.value as Role })}
                  className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                >
                  <option value="" disabled>Select Role</option>
                  {roleOptions.map((role) => (
                    <option key={role} value={role}>{role}</option>
                  ))}
                </select>
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="password" className="text-right">
                Password
              </Label>
              <Input
                id="password"
                type="password"
                value={newStaff.password}
                onChange={(e) => setNewStaff({ ...newStaff, password: e.target.value })}
                className="col-span-3"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddStaffOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddStaff}>Add Staff</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View User Details Dialog */}
      <Dialog open={!!viewingUser} onOpenChange={() => setViewingUser(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>User Details</DialogTitle>
            <DialogDescription>
              View detailed information about this user.
            </DialogDescription>
          </DialogHeader>
          {viewingUser && (
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-medium">Name:</Label>
                <div className="col-span-3">{viewingUser.name}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-medium">Email:</Label>
                <div className="col-span-3">{viewingUser.email}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-medium">Role:</Label>
                <div className="col-span-3 capitalize">{viewingUser.role}</div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-medium">Status:</Label>
                <div className="col-span-3">
                  <div className={`px-2 py-1 rounded-full text-xs font-medium inline-block ${
                    viewingUser.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                  }`}>
                    {viewingUser.status.charAt(0).toUpperCase() + viewingUser.status.slice(1)}
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label className="text-right font-medium">User ID:</Label>
                <div className="col-span-3 font-mono text-xs">{viewingUser.id}</div>
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setViewingUser(null)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit User Dialog */}
      <Dialog open={!!editingUser} onOpenChange={() => setEditingUser(null)}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Edit User</DialogTitle>
            <DialogDescription>
              Update user information and role.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                Name
              </Label>
              <Input
                id="edit-name"
                value={editForm.name}
                onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-email" className="text-right">
                Email
              </Label>
              <Input
                id="edit-email"
                type="email"
                value={editForm.email}
                onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                className="col-span-3"
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-role" className="text-right">
                Role
              </Label>
              <div className="col-span-3">
                <Select value={editForm.role} onValueChange={(value: Role) => setEditForm({ ...editForm, role: value })}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select role" />
                  </SelectTrigger>
                  <SelectContent>
                    {roleOptions.map((role) => (
                      <SelectItem key={role} value={role}>
                        {role.charAt(0).toUpperCase() + role.slice(1)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditingUser(null)}>
              Cancel
            </Button>
            <Button
              onClick={handleUpdateUser}
              disabled={isUpdating || !editForm.name.trim() || !editForm.email.trim() || !editForm.role}
            >
              {isUpdating ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                  Updating...
                </>
              ) : (
                "Update User"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
