import { useState, useEffect } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>it<PERSON> 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/hooks/use-toast";
import { expenseService } from "@/lib/firebase";
import { useAuth } from "@/context/AuthContext";
import type { Expense } from '@/types/firebase';
import { X, Save, DollarSign } from "lucide-react";

interface ExpenseFormData {
  title: string;
  description: string;
  amount: string;
  category: string;
  date: string;
  paymentMethod: 'cash' | 'card' | 'bank_transfer' | 'check' | 'other';
  vendor: string;
  receiptNumber: string;
  isRecurring: boolean;
  recurringFrequency: 'daily' | 'weekly' | 'monthly' | 'yearly';
  tags: string;
  status: 'pending' | 'approved' | 'rejected';
}

const initialFormData: ExpenseFormData = {
  title: "",
  description: "",
  amount: "",
  category: "",
  date: new Date().toISOString().split('T')[0],
  paymentMethod: "card",
  vendor: "",
  receiptNumber: "",
  isRecurring: false,
  recurringFrequency: "monthly",
  tags: "",
  status: "pending"
};

const expenseCategories = [
  "Rent & Utilities",
  "Inventory",
  "Marketing",
  "Maintenance",
  "Office Supplies",
  "Equipment",
  "Insurance",
  "Professional Services",
  "Travel",
  "Meals & Entertainment",
  "Software & Subscriptions",
  "Banking & Fees",
  "Other"
];

interface AddExpenseFormProps {
  expense?: Expense | null;
  onClose: () => void;
  onExpenseAdded: () => void;
}

export function AddExpenseForm({ expense, onClose, onExpenseAdded }: AddExpenseFormProps) {
  const { toast } = useToast();
  const { currentUser } = useAuth();
  const [formData, setFormData] = useState<ExpenseFormData>(initialFormData);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    if (expense) {
      setFormData({
        title: expense.title,
        description: expense.description || "",
        amount: expense.amount.toString(),
        category: expense.category,
        date: expense.date,
        paymentMethod: expense.paymentMethod,
        vendor: expense.vendor || "",
        receiptNumber: expense.receiptNumber || "",
        isRecurring: expense.isRecurring,
        recurringFrequency: expense.recurringFrequency || "monthly",
        tags: expense.tags?.join(", ") || "",
        status: expense.status
      });
    } else {
      setFormData(initialFormData);
    }
  }, [expense]);

  const handleInputChange = (field: keyof ExpenseFormData, value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.title.trim() || !formData.amount || !formData.category) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    const amount = parseFloat(formData.amount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: "Validation Error",
        description: "Please enter a valid amount",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const expenseData = {
        title: formData.title.trim(),
        description: formData.description.trim(),
        amount: amount,
        category: formData.category,
        date: formData.date,
        paymentMethod: formData.paymentMethod,
        vendor: formData.vendor.trim(),
        receiptNumber: formData.receiptNumber.trim(),
        isRecurring: formData.isRecurring,
        recurringFrequency: formData.isRecurring ? formData.recurringFrequency : undefined,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag.length > 0),
        createdBy: currentUser?.uid || currentUser?.email || 'unknown',
        status: formData.status
      };

      if (expense) {
        await expenseService.update(expense.id, expenseData);
        toast({
          title: "Success",
          description: "Expense updated successfully",
        });
      } else {
        await expenseService.add(expenseData);
        toast({
          title: "Success",
          description: "Expense added successfully",
        });
      }

      onExpenseAdded();
    } catch (error) {
      console.error("Error saving expense:", error);
      toast({
        title: "Error",
        description: "Failed to save expense. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="title">Title *</Label>
          <Input
            id="title"
            value={formData.title}
            onChange={(e) => handleInputChange("title", e.target.value)}
            placeholder="Enter expense title"
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="amount">Amount *</Label>
          <div className="relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              id="amount"
              type="number"
              step="0.01"
              min="0"
              value={formData.amount}
              onChange={(e) => handleInputChange("amount", e.target.value)}
              placeholder="0.00"
              className="pl-10"
              required
            />
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="category">Category *</Label>
          <Select value={formData.category} onValueChange={(value) => handleInputChange("category", value)}>
            <SelectTrigger>
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {expenseCategories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="date">Date *</Label>
          <Input
            id="date"
            type="date"
            value={formData.date}
            onChange={(e) => handleInputChange("date", e.target.value)}
            required
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="paymentMethod">Payment Method</Label>
          <Select value={formData.paymentMethod} onValueChange={(value) => handleInputChange("paymentMethod", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="cash">Cash</SelectItem>
              <SelectItem value="card">Card</SelectItem>
              <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
              <SelectItem value="check">Check</SelectItem>
              <SelectItem value="other">Other</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="status">Status</Label>
          <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="rejected">Rejected</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="vendor">Vendor</Label>
          <Input
            id="vendor"
            value={formData.vendor}
            onChange={(e) => handleInputChange("vendor", e.target.value)}
            placeholder="Enter vendor name"
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="receiptNumber">Receipt Number</Label>
          <Input
            id="receiptNumber"
            value={formData.receiptNumber}
            onChange={(e) => handleInputChange("receiptNumber", e.target.value)}
            placeholder="Enter receipt number"
          />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleInputChange("description", e.target.value)}
          placeholder="Enter expense description"
          rows={3}
        />
      </div>

      <div className="space-y-2">
        <Label htmlFor="tags">Tags</Label>
        <Input
          id="tags"
          value={formData.tags}
          onChange={(e) => handleInputChange("tags", e.target.value)}
          placeholder="Enter tags separated by commas"
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="isRecurring"
          checked={formData.isRecurring}
          onCheckedChange={(checked) => handleInputChange("isRecurring", checked as boolean)}
        />
        <Label htmlFor="isRecurring">This is a recurring expense</Label>
      </div>

      {formData.isRecurring && (
        <div className="space-y-2">
          <Label htmlFor="recurringFrequency">Recurring Frequency</Label>
          <Select value={formData.recurringFrequency} onValueChange={(value) => handleInputChange("recurringFrequency", value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="daily">Daily</SelectItem>
              <SelectItem value="weekly">Weekly</SelectItem>
              <SelectItem value="monthly">Monthly</SelectItem>
              <SelectItem value="yearly">Yearly</SelectItem>
            </SelectContent>
          </Select>
        </div>
      )}

      <div className="flex justify-end gap-3 pt-4">
        <Button type="button" variant="outline" onClick={onClose}>
          <X className="mr-2 h-4 w-4" />
          Cancel
        </Button>
        <Button type="submit" disabled={isSubmitting}>
          <Save className="mr-2 h-4 w-4" />
          {isSubmitting ? "Saving..." : expense ? "Update Expense" : "Add Expense"}
        </Button>
      </div>
    </form>
  );
}
