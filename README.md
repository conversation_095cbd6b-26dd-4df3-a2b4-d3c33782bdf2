# Commerce Collective - Stationery Shop Management System

A comprehensive stationery shop management system built with modern web technologies for inventory management, sales tracking, and customer management.

## Features

- **Inventory Management**: Track products, stock levels, and low stock alerts
- **Sales Management**: Process sales, generate receipts, and track revenue
- **Customer Management**: Maintain customer records and purchase history
- **Analytics Dashboard**: View sales trends, popular products, and business insights
- **User Authentication**: Secure login system with Firebase Authentication
- **Real-time Updates**: Live data synchronization across all users

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- npm or yarn package manager

### Installation

1. Clone the repository:
```sh
git clone <YOUR_GIT_URL>
cd commerce-collective
```

2. Install dependencies:
```sh
npm install
```

3. Start the development server:
```sh
npm run dev
```

4. Open your browser and navigate to `http://localhost:8080`

## Development

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run build:dev` - Build for development
- `npm run preview` - Preview production build
- `npm run lint` - Run ESLint

## Technologies Used

This stationery shop management system is built with modern web technologies:

- **Frontend Framework**: React 18 with TypeScript
- **Build Tool**: Vite
- **UI Components**: shadcn/ui with Radix UI primitives
- **Styling**: Tailwind CSS with custom animations
- **Backend**: Firebase (Authentication & Firestore)
- **State Management**: TanStack Query for server state
- **Charts & Analytics**: Recharts
- **Form Handling**: React Hook Form with Zod validation
- **Routing**: React Router DOM

## Project Structure

```
src/
├── components/          # Reusable UI components
├── pages/              # Page components
├── lib/                # Utility functions and configurations
├── hooks/              # Custom React hooks
└── types/              # TypeScript type definitions
```

## Deployment

This project can be deployed to any static hosting service:

- **Netlify**: Connect your repository for automatic deployments
- **Vercel**: Import your project for seamless deployment
- **GitHub Pages**: Use GitHub Actions for automated deployment
- **Firebase Hosting**: Deploy alongside your Firebase backend

### Build for Production

```sh
npm run build
```

The built files will be in the `dist` directory.

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License.
