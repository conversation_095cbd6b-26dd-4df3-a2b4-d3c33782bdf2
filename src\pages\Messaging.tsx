
import { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  Card<PERSON>ooter,
  CardHeader,
  CardTitle
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { useQuery } from "@tanstack/react-query";
import { useAuth } from "@/context/AuthContext";
import {
  Bell,
  Send,
  Calendar,
  Clock,
  Megaphone,
  Edit,
  Trash2,
  X,
  Check
} from "lucide-react";
import { getFirestore, collection, getDocs, addDoc, query, orderBy, doc, updateDoc, deleteDoc } from "firebase/firestore";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

type Announcement = {
  id: string;
  title: string;
  content: string;
  author: string;
  timestamp: string;
  isActive: boolean;
};

export default function Messaging() {
  const [newAnnouncementTitle, setNewAnnouncementTitle] = useState("");
  const [newAnnouncementContent, setNewAnnouncementContent] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [editingAnnouncement, setEditingAnnouncement] = useState<Announcement | null>(null);
  const [editTitle, setEditTitle] = useState("");
  const [editContent, setEditContent] = useState("");
  const [isUpdating, setIsUpdating] = useState(false);
  const [isDeleting, setIsDeleting] = useState<string | null>(null);
  const { toast } = useToast();
  const { currentUser, isAdmin } = useAuth();

  const { data: announcements = [], isLoading: isLoadingAnnouncements, refetch: refetchAnnouncements } = useQuery({
    queryKey: ["announcements"],
    queryFn: async () => {
      const db = getFirestore();
      const announcementsCollection = collection(db, "announcements");
      const announcementsQuery = query(announcementsCollection, orderBy("timestamp", "desc"));
      const snapshot = await getDocs(announcementsQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<Announcement, "id">)
      }));
    }
  });
  const handleCreateAnnouncement = async () => {
    if (!newAnnouncementTitle.trim() || !newAnnouncementContent.trim()) {
      toast({
        title: "Error",
        description: "Please fill in both title and content",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);
      const db = getFirestore();

      await addDoc(collection(db, "announcements"), {
        title: newAnnouncementTitle,
        content: newAnnouncementContent,
        author: currentUser?.displayName || "Shop Staff",
        timestamp: new Date().toISOString(),
        isActive: true,
      });

      toast({
        title: "Success",
        description: "Announcement created successfully",
      });

      setNewAnnouncementTitle("");
      setNewAnnouncementContent("");
      refetchAnnouncements();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create announcement",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditAnnouncement = (announcement: Announcement) => {
    setEditingAnnouncement(announcement);
    setEditTitle(announcement.title);
    setEditContent(announcement.content);
  };

  const handleUpdateAnnouncement = async () => {
    if (!editTitle.trim() || !editContent.trim()) {
      toast({
        title: "Error",
        description: "Please fill in both title and content",
        variant: "destructive",
      });
      return;
    }

    if (!editingAnnouncement) return;

    try {
      setIsUpdating(true);
      const db = getFirestore();
      const announcementRef = doc(db, "announcements", editingAnnouncement.id);

      await updateDoc(announcementRef, {
        title: editTitle,
        content: editContent,
        timestamp: new Date().toISOString(), // Update timestamp when edited
      });

      toast({
        title: "Success",
        description: "Announcement updated successfully",
      });

      setEditingAnnouncement(null);
      setEditTitle("");
      setEditContent("");
      refetchAnnouncements();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to update announcement",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  const handleDeleteAnnouncement = async (announcementId: string) => {
    try {
      setIsDeleting(announcementId);
      const db = getFirestore();
      const announcementRef = doc(db, "announcements", announcementId);

      await deleteDoc(announcementRef);

      toast({
        title: "Success",
        description: "Announcement deleted successfully",
      });

      refetchAnnouncements();
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to delete announcement",
        variant: "destructive",
      });
    } finally {
      setIsDeleting(null);
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Invalid date";
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return "Invalid date";
      return new Intl.DateTimeFormat('en-US', {
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      }).format(date);
    } catch (error) {
      return "Invalid date";
    }
  };

  return (
    <div className="space-y-4 sm:space-y-6 animate-fade-in">
      <div>
        <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Store Announcements</h1>
        <p className="text-muted-foreground text-sm sm:text-base">
          Manage and view stationery shop announcements for the team.
        </p>
      </div>

      <div className="space-y-4">
        <Card>
          <CardHeader>
            <CardTitle className="text-lg sm:text-xl">Store Announcements</CardTitle>
            <CardDescription>Recent announcements for the team</CardDescription>
          </CardHeader>
          <CardContent>
            {isLoadingAnnouncements ? (
              <div className="flex justify-center p-4">
                <div className="h-6 w-6 animate-spin rounded-full border-2 border-primary border-t-transparent"></div>
              </div>
            ) : announcements.length === 0 ? (
              <div className="text-center py-6 sm:py-8">
                <Bell className="h-8 w-8 sm:h-12 sm:w-12 mx-auto text-muted-foreground mb-4" />
                <p className="text-muted-foreground text-sm sm:text-base">No announcements yet</p>
              </div>
            ) : (
              <div className="space-y-4">
                {announcements.map((announcement) => (
                  <div key={announcement.id} className="border rounded-lg p-3 sm:p-4">
                    <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-3">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-base sm:text-lg">{announcement.title}</h3>
                        <p className="text-muted-foreground mt-2 text-sm sm:text-base">{announcement.content}</p>
                        <div className="flex flex-col sm:flex-row sm:items-center mt-3 text-xs sm:text-sm text-muted-foreground gap-1 sm:gap-0">
                          <div className="flex items-center">
                            <Calendar className="h-3 w-3 sm:h-4 sm:w-4 mr-1" />
                            <span>{formatDate(announcement.timestamp)}</span>
                          </div>
                          <span className="hidden sm:inline mx-2">•</span>
                          <span>By {announcement.author}</span>
                        </div>
                      </div>
                      <div className="flex items-center justify-between sm:justify-end gap-2">
                        <Badge variant="secondary" className="text-xs">
                          <Bell className="h-3 w-3 mr-1" />
                          <span className="hidden sm:inline">Active</span>
                          <span className="sm:hidden">On</span>
                        </Badge>
                        {isAdmin() && (
                          <div className="flex gap-1">
                            <Dialog>
                              <DialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleEditAnnouncement(announcement)}
                                  className="h-8 w-8 p-0 sm:h-10 sm:w-10"
                                >
                                  <Edit className="h-3 w-3 sm:h-4 sm:w-4" />
                                </Button>
                              </DialogTrigger>
                              <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
                                <DialogHeader>
                                  <DialogTitle className="text-lg sm:text-xl">Edit Announcement</DialogTitle>
                                </DialogHeader>
                                <div className="space-y-4 py-4">
                                  <div className="space-y-2">
                                    <Label htmlFor="edit-title">Title</Label>
                                    <Input
                                      id="edit-title"
                                      value={editTitle}
                                      onChange={(e) => setEditTitle(e.target.value)}
                                      placeholder="Enter announcement title"
                                    />
                                  </div>
                                  <div className="space-y-2">
                                    <Label htmlFor="edit-content">Content</Label>
                                    <Textarea
                                      id="edit-content"
                                      value={editContent}
                                      onChange={(e) => setEditContent(e.target.value)}
                                      placeholder="Enter announcement content"
                                      rows={4}
                                    />
                                  </div>
                                </div>
                                <div className="flex flex-col sm:flex-row justify-end gap-2">
                                  <Button
                                    variant="outline"
                                    onClick={() => setEditingAnnouncement(null)}
                                    className="w-full sm:w-auto"
                                  >
                                    Cancel
                                  </Button>
                                  <Button
                                    onClick={handleUpdateAnnouncement}
                                    disabled={isUpdating || !editTitle.trim() || !editContent.trim()}
                                    className="w-full sm:w-auto"
                                  >
                                    {isUpdating ? (
                                      <>
                                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                                        Updating...
                                      </>
                                    ) : (
                                      <>
                                        <Check className="mr-2 h-4 w-4" />
                                        Update
                                      </>
                                    )}
                                  </Button>
                                </div>
                              </DialogContent>
                            </Dialog>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-destructive hover:text-destructive h-8 w-8 p-0 sm:h-10 sm:w-10"
                                >
                                  <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Announcement</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete this announcement? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteAnnouncement(announcement.id)}
                                    disabled={isDeleting === announcement.id}
                                    className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                                  >
                                    {isDeleting === announcement.id ? (
                                      <>
                                        <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                                        Deleting...
                                      </>
                                    ) : (
                                      "Delete"
                                    )}
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {isAdmin() && (
          <Card>
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Create New Announcement</CardTitle>
              <CardDescription>Share important updates with your team</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="announcement-title" className="text-sm sm:text-base">Title</Label>
                <Input
                  id="announcement-title"
                  value={newAnnouncementTitle}
                  onChange={(e) => setNewAnnouncementTitle(e.target.value)}
                  placeholder="Enter announcement title"
                  className="text-sm sm:text-base"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="announcement-content" className="text-sm sm:text-base">Content</Label>
                <Textarea
                  id="announcement-content"
                  value={newAnnouncementContent}
                  onChange={(e) => setNewAnnouncementContent(e.target.value)}
                  placeholder="Enter announcement content"
                  rows={4}
                  className="text-sm sm:text-base resize-none"
                />
              </div>
            </CardContent>
            <CardFooter>
              <Button
                onClick={handleCreateAnnouncement}
                disabled={isSubmitting || !newAnnouncementTitle.trim() || !newAnnouncementContent.trim()}
                className="w-full text-sm sm:text-base"
              >
                {isSubmitting ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-background border-t-transparent"></div>
                    Creating...
                  </>
                ) : (
                  <>
                    <Send className="mr-2 h-4 w-4" />
                    <span className="sm:hidden">Create</span>
                    <span className="hidden sm:inline">Create Announcement</span>
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        )}
      </div>
    </div>
  );
}
