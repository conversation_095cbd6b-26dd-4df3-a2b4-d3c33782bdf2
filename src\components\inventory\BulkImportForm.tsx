import { useState, useRef } from "react";
import { 
  <PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Card<PERSON>itle 
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { firestore } from "@/lib/firebase";
import { Upload, Download, FileText, AlertCircle, CheckCircle, X } from "lucide-react";
import Papa from "papaparse";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";

interface ImportedProduct {
  name: string;
  category: string;
  sku: string;
  buyingPrice: number;
  sellingPrice: number;
  stock: number;
  description?: string;
}

interface ValidationError {
  row: number;
  field: string;
  message: string;
}

interface BulkImportFormProps {
  onClose: () => void;
  onImportComplete: () => void;
}

const categories = [
  "Pens & Pencils",
  "Paper Products", 
  "Office Supplies",
  "Art Supplies",
  "School Supplies",
  "Notebooks & Journals",
  "Filing & Organization",
  "Desk Accessories",
  "Printing & Binding",
  "Stickers & Labels",
];

export function BulkImportForm({ onClose, onImportComplete }: BulkImportFormProps) {
  const [file, setFile] = useState<File | null>(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [importProgress, setImportProgress] = useState(0);
  const [parsedData, setParsedData] = useState<ImportedProduct[]>([]);
  const [validationErrors, setValidationErrors] = useState<ValidationError[]>([]);
  const [importStep, setImportStep] = useState<'upload' | 'preview' | 'importing' | 'complete'>('upload');
  const [importResults, setImportResults] = useState<{ success: number; failed: number }>({ success: 0, failed: 0 });
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();

  const downloadTemplate = () => {
    const headers = ["name", "category", "sku", "buyingPrice", "sellingPrice", "stock", "description"];
    const sampleData = [
      ["BIC Blue Pen", "Pens & Pencils", "PEN001", "1.50", "2.50", "100", "Classic blue ballpoint pen"],
      ["A4 Copy Paper", "Paper Products", "PAP001", "8.00", "12.00", "50", "High-quality white A4 paper, 500 sheets"],
      ["Stapler", "Office Supplies", "OFF001", "15.00", "25.00", "20", "Heavy-duty stapler with staples included"],
      ["Colored Pencils Set", "Art Supplies", "ART001", "10.00", "18.00", "30", "Set of 24 colored pencils"],
      ["Spiral Notebook", "Notebooks & Journals", "NOT001", "3.00", "5.50", "75", "A5 spiral notebook, 200 pages"]
    ];

    const csvRows = [headers.join(",")];
    sampleData.forEach(row => {
      const escapedRow = row.map(cell => `"${cell}"`);
      csvRows.push(escapedRow.join(","));
    });

    const csvContent = csvRows.join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);

    link.setAttribute("href", url);
    link.setAttribute("download", "inventory_import_template.csv");
    link.style.visibility = "hidden";

    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Template Downloaded",
      description: "CSV template with sample data has been downloaded successfully"
    });
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = event.target.files?.[0];
    if (selectedFile) {
      if (selectedFile.type !== "text/csv" && !selectedFile.name.endsWith('.csv')) {
        toast({
          title: "Invalid File Type",
          description: "Please select a CSV file",
          variant: "destructive"
        });
        return;
      }
      setFile(selectedFile);
    }
  };

  const validateProduct = (product: any, rowIndex: number): ValidationError[] => {
    const errors: ValidationError[] = [];

    // Required fields validation
    if (!product.name?.trim()) {
      errors.push({ row: rowIndex, field: 'name', message: 'Product name is required' });
    } else if (product.name.trim().length > 100) {
      errors.push({ row: rowIndex, field: 'name', message: 'Product name must be less than 100 characters' });
    }

    if (!product.category?.trim()) {
      errors.push({ row: rowIndex, field: 'category', message: 'Category is required' });
    } else if (!categories.includes(product.category.trim())) {
      errors.push({ row: rowIndex, field: 'category', message: `Invalid category. Must be one of: ${categories.join(', ')}` });
    }

    if (!product.sku?.trim()) {
      errors.push({ row: rowIndex, field: 'sku', message: 'SKU is required' });
    } else if (product.sku.trim().length > 50) {
      errors.push({ row: rowIndex, field: 'sku', message: 'SKU must be less than 50 characters' });
    } else if (!/^[A-Za-z0-9_-]+$/.test(product.sku.trim())) {
      errors.push({ row: rowIndex, field: 'sku', message: 'SKU can only contain letters, numbers, hyphens, and underscores' });
    }

    // Numeric fields validation
    const buyingPrice = parseFloat(product.buyingPrice);
    if (isNaN(buyingPrice) || buyingPrice < 0) {
      errors.push({ row: rowIndex, field: 'buyingPrice', message: 'Buying price must be a valid positive number' });
    } else if (buyingPrice > 999999.99) {
      errors.push({ row: rowIndex, field: 'buyingPrice', message: 'Buying price must be less than 1,000,000' });
    }

    const sellingPrice = parseFloat(product.sellingPrice);
    if (isNaN(sellingPrice) || sellingPrice < 0) {
      errors.push({ row: rowIndex, field: 'sellingPrice', message: 'Selling price must be a valid positive number' });
    } else if (sellingPrice > 999999.99) {
      errors.push({ row: rowIndex, field: 'sellingPrice', message: 'Selling price must be less than 1,000,000' });
    } else if (!isNaN(buyingPrice) && sellingPrice < buyingPrice) {
      errors.push({ row: rowIndex, field: 'sellingPrice', message: 'Selling price should not be less than buying price' });
    }

    const stock = parseInt(product.stock);
    if (isNaN(stock) || stock < 0) {
      errors.push({ row: rowIndex, field: 'stock', message: 'Stock must be a valid non-negative integer' });
    } else if (stock > 999999) {
      errors.push({ row: rowIndex, field: 'stock', message: 'Stock must be less than 1,000,000' });
    }

    // Optional description validation
    if (product.description && product.description.length > 500) {
      errors.push({ row: rowIndex, field: 'description', message: 'Description must be less than 500 characters' });
    }

    return errors;
  };

  const parseCSV = () => {
    if (!file) return;

    setIsProcessing(true);

    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      transformHeader: (header) => header.trim().toLowerCase(),
      complete: (results) => {
        if (results.errors.length > 0) {
          toast({
            title: "Parse Error",
            description: `CSV parsing errors: ${results.errors.map(e => e.message).join(', ')}`,
            variant: "destructive"
          });
          setIsProcessing(false);
          return;
        }

        const data = results.data as any[];

        if (data.length === 0) {
          toast({
            title: "Empty File",
            description: "The CSV file appears to be empty or contains no valid data rows",
            variant: "destructive"
          });
          setIsProcessing(false);
          return;
        }

        // Check for required headers
        const requiredHeaders = ['name', 'category', 'sku', 'buyingprice', 'sellingprice', 'stock'];
        const firstRow = data[0];
        const missingHeaders = requiredHeaders.filter(header => !(header in firstRow));

        if (missingHeaders.length > 0) {
          toast({
            title: "Missing Headers",
            description: `Missing required columns: ${missingHeaders.join(', ')}. Please check your CSV format.`,
            variant: "destructive"
          });
          setIsProcessing(false);
          return;
        }

        const errors: ValidationError[] = [];
        const validProducts: ImportedProduct[] = [];
        const skuSet = new Set<string>();

        data.forEach((row, index) => {
          // Normalize the row data to match expected field names
          const normalizedRow = {
            name: row.name,
            category: row.category,
            sku: row.sku,
            buyingPrice: row.buyingprice,
            sellingPrice: row.sellingprice,
            stock: row.stock,
            description: row.description
          };

          const rowErrors = validateProduct(normalizedRow, index + 2); // +2 because of header and 0-based index

          // Check for duplicate SKUs within the import file
          if (normalizedRow.sku?.trim()) {
            const trimmedSku = normalizedRow.sku.trim();
            if (skuSet.has(trimmedSku)) {
              rowErrors.push({ row: index + 2, field: 'sku', message: `Duplicate SKU found in import file: ${trimmedSku}` });
            } else {
              skuSet.add(trimmedSku);
            }
          }

          errors.push(...rowErrors);

          if (rowErrors.length === 0) {
            validProducts.push({
              name: normalizedRow.name.trim(),
              category: normalizedRow.category.trim(),
              sku: normalizedRow.sku.trim(),
              buyingPrice: parseFloat(normalizedRow.buyingPrice),
              sellingPrice: parseFloat(normalizedRow.sellingPrice),
              stock: parseInt(normalizedRow.stock),
              description: normalizedRow.description?.trim() || ""
            });
          }
        });

        setValidationErrors(errors);
        setParsedData(validProducts);
        setImportStep('preview');
        setIsProcessing(false);
      },
      error: (error) => {
        toast({
          title: "Parse Error",
          description: `Failed to parse CSV file: ${error.message}`,
          variant: "destructive"
        });
        setIsProcessing(false);
      }
    });
  };

  const checkForDuplicateSKUs = async (products: ImportedProduct[]): Promise<string[]> => {
    try {
      const existingProducts = await firestore.collection("products").get();
      const existingSKUs = new Set(existingProducts.docs.map(doc => doc.data().sku));
      
      const duplicateSKUs: string[] = [];
      const importSKUs = new Set<string>();
      
      products.forEach(product => {
        if (existingSKUs.has(product.sku) || importSKUs.has(product.sku)) {
          duplicateSKUs.push(product.sku);
        }
        importSKUs.add(product.sku);
      });
      
      return duplicateSKUs;
    } catch (error) {
      console.error("Error checking for duplicate SKUs:", error);
      return [];
    }
  };

  const importProducts = async () => {
    if (parsedData.length === 0) return;
    
    setImportStep('importing');
    setImportProgress(0);
    
    // Check for duplicate SKUs
    const duplicateSKUs = await checkForDuplicateSKUs(parsedData);
    if (duplicateSKUs.length > 0) {
      toast({
        title: "Duplicate SKUs Found",
        description: `The following SKUs already exist: ${duplicateSKUs.join(', ')}`,
        variant: "destructive"
      });
      setImportStep('preview');
      return;
    }
    
    let successCount = 0;
    let failedCount = 0;
    
    for (let i = 0; i < parsedData.length; i++) {
      const product = parsedData[i];
      
      try {
        await firestore.collection("products").add({
          name: product.name,
          category: product.category,
          sku: product.sku,
          buyingPrice: product.buyingPrice,
          sellingPrice: product.sellingPrice,
          price: product.sellingPrice, // Legacy field for backward compatibility
          stock: product.stock,
          description: product.description,
          createdAt: new Date().toISOString()
        });
        
        successCount++;
      } catch (error) {
        console.error(`Failed to import product ${product.name}:`, error);
        failedCount++;
      }
      
      setImportProgress(((i + 1) / parsedData.length) * 100);
    }
    
    setImportResults({ success: successCount, failed: failedCount });
    setImportStep('complete');
    
    toast({
      title: "Import Complete",
      description: `Successfully imported ${successCount} products. ${failedCount} failed.`,
      variant: successCount > 0 ? "default" : "destructive"
    });
    
    if (successCount > 0) {
      onImportComplete();
    }
  };

  const resetForm = () => {
    setFile(null);
    setParsedData([]);
    setValidationErrors([]);
    setImportStep('upload');
    setImportProgress(0);
    setImportResults({ success: 0, failed: 0 });
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  return (
    <Card className="w-full max-w-4xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Bulk Import Products
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {importStep === 'upload' && (
          <>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label htmlFor="csv-file">Select CSV File</Label>
                <Button variant="outline" size="sm" onClick={downloadTemplate}>
                  <Download className="mr-2 h-4 w-4" />
                  Download Template
                </Button>
              </div>
              
              <Input
                id="csv-file"
                type="file"
                accept=".csv"
                onChange={handleFileSelect}
                ref={fileInputRef}
              />
              
              {file && (
                <div className="flex items-center gap-2 p-3 bg-muted rounded-lg">
                  <FileText className="h-4 w-4" />
                  <span className="text-sm">{file.name}</span>
                  <Badge variant="secondary">{(file.size / 1024).toFixed(1)} KB</Badge>
                </div>
              )}
            </div>
            
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                CSV file should contain columns: name, category, sku, buyingPrice, sellingPrice, stock, description.
                Download the template above for the correct format.
              </AlertDescription>
            </Alert>
          </>
        )}
        
        {importStep === 'preview' && (
          <>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Import Preview</h3>
                <div className="flex gap-2">
                  <Badge variant="outline">{parsedData.length} valid products</Badge>
                  {validationErrors.length > 0 && (
                    <Badge variant="destructive">{validationErrors.length} errors</Badge>
                  )}
                </div>
              </div>
              
              {validationErrors.length > 0 && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>
                    <div className="space-y-1">
                      <p className="font-medium">Validation Errors:</p>
                      {validationErrors.slice(0, 5).map((error, index) => (
                        <p key={index} className="text-sm">
                          Row {error.row}, {error.field}: {error.message}
                        </p>
                      ))}
                      {validationErrors.length > 5 && (
                        <p className="text-sm">...and {validationErrors.length - 5} more errors</p>
                      )}
                    </div>
                  </AlertDescription>
                </Alert>
              )}
              
              {parsedData.length > 0 && (
                <div className="border rounded-lg p-4 max-h-60 overflow-y-auto">
                  <h4 className="font-medium mb-2">Valid Products to Import:</h4>
                  <div className="space-y-2">
                    {parsedData.slice(0, 10).map((product, index) => (
                      <div key={index} className="flex items-center justify-between text-sm p-2 bg-muted rounded">
                        <span>{product.name} ({product.sku})</span>
                        <span>{product.category}</span>
                        <span>Stock: {product.stock}</span>
                      </div>
                    ))}
                    {parsedData.length > 10 && (
                      <p className="text-sm text-muted-foreground">...and {parsedData.length - 10} more products</p>
                    )}
                  </div>
                </div>
              )}
            </div>
          </>
        )}
        
        {importStep === 'importing' && (
          <div className="space-y-4">
            <div className="text-center">
              <h3 className="text-lg font-semibold mb-2">Importing Products...</h3>
              <Progress value={importProgress} className="w-full" />
              <p className="text-sm text-muted-foreground mt-2">
                {Math.round(importProgress)}% complete
              </p>
            </div>
          </div>
        )}
        
        {importStep === 'complete' && (
          <div className="space-y-4">
            <div className="text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Import Complete!</h3>
              <div className="flex justify-center gap-4">
                <Badge variant="default">{importResults.success} successful</Badge>
                {importResults.failed > 0 && (
                  <Badge variant="destructive">{importResults.failed} failed</Badge>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
      <CardFooter className="flex justify-end space-x-2">
        {importStep === 'upload' && (
          <>
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              onClick={parseCSV} 
              disabled={!file || isProcessing}
            >
              {isProcessing ? "Processing..." : "Parse CSV"}
            </Button>
          </>
        )}
        
        {importStep === 'preview' && (
          <>
            <Button variant="outline" onClick={resetForm}>
              Start Over
            </Button>
            <Button 
              onClick={importProducts} 
              disabled={parsedData.length === 0}
            >
              Import {parsedData.length} Products
            </Button>
          </>
        )}
        
        {importStep === 'importing' && (
          <Button variant="outline" disabled>
            Importing...
          </Button>
        )}
        
        {importStep === 'complete' && (
          <>
            <Button variant="outline" onClick={resetForm}>
              Import More
            </Button>
            <Button onClick={onClose}>
              Close
            </Button>
          </>
        )}
      </CardFooter>
    </Card>
  );
}
