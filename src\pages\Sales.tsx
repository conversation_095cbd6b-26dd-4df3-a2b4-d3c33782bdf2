
import { useState } from "react";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from "@/components/ui/card";
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON>onte<PERSON>, 
  <PERSON><PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { DataTable } from "@/components/common/DataTable";
import { MetricCard } from "@/components/common/MetricCard";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { firestore } from "@/lib/firebase";
import { Dialog, DialogContent, DialogTrigger, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { RecordSaleForm } from "@/components/sales/RecordSaleForm";
import {
  Bar<PERSON>hart,
  LineChart,
  ArrowUpRight,
  ArrowDownRight,
  DollarSign,
  ShoppingCart,
  Users,
  Calendar,
  Plus,
  Eye,
  Package
} from "lucide-react";
import { <PERSON><PERSON><PERSON> as RechartsBarChart, <PERSON>, XAxi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart as RechartsLineChart, Line, PieChart, Pie, Cell } from "recharts";
import { format, subDays, startOfDay, endOfDay } from "date-fns";
import type { Sale, SaleItem, Product } from '@/types/firebase';
import { getFirestore, collection, getDocs } from "firebase/firestore";

export default function Sales() {
  const [activeTab, setActiveTab] = useState("overview");
  const [showSaleForm, setShowSaleForm] = useState(false);
  const [viewingSale, setViewingSale] = useState<Sale | null>(null);
  
  const { data: salesData = [], isLoading: isLoadingSales, refetch: refetchSales } = useQuery<Sale[]>({
    queryKey: ["sales"],
    queryFn: async () => {
      // Direct Firestore access for more reliable fetching
      const db = getFirestore();
      const salesCollection = collection(db, "sales");
      const querySnapshot = await getDocs(salesCollection);

      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<Sale, 'id'>)
      }));
    }
  });

  const { data: productsData = [] } = useQuery<Product[]>({
    queryKey: ["products"],
    queryFn: async () => {
      const db = getFirestore();
      const productsCollection = collection(db, "products");
      const querySnapshot = await getDocs(productsCollection);

      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...(doc.data() as Omit<Product, 'id'>)
      }));
    }
  });

  const salesColumns = [
    {
      id: "date",
      header: "Date",
      cell: (sale: any) => (
        <div className="flex items-center min-w-0">
          <Calendar className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground flex-shrink-0" />
          <span className="text-xs sm:text-sm truncate">{sale.date}</span>
        </div>
      ),
    },
    {
      id: "customer",
      header: "Customer",
      cell: (sale: any) => (
        <div className="flex items-center min-w-0">
          <Users className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground flex-shrink-0" />
          <span className="text-xs sm:text-sm truncate">{sale.customer}</span>
        </div>
      ),
    },
    {
      id: "items",
      header: "Items",
      cell: (sale: any) => (
        <div className="flex items-center">
          <ShoppingCart className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
          <span className="text-xs sm:text-sm">{sale.items}</span>
        </div>
      ),
    },
    {
      id: "amount",
      header: "Amount",
      cell: (sale: any) => (
        <div className="flex items-center font-medium">
          <DollarSign className="mr-1 h-3 w-3 sm:h-4 sm:w-4 text-green-500" />
          <span className="text-xs sm:text-sm">Tsh {sale.amount?.toFixed(2) || '0.00'}</span>
        </div>
      ),
    },
    {
      id: "status",
      header: "Status",
      cell: (sale: any) => {
        const statusMap: Record<string, { label: string, variant: "default" | "secondary" | "outline" | "destructive" }> = {
          completed: { label: "Completed", variant: "default" },
          pending: { label: "Pending", variant: "secondary" },
          canceled: { label: "Canceled", variant: "destructive" },
        };

        const status = statusMap[sale.status || "completed"];

        return (
          <Badge variant={status.variant} className="text-xs">
            <span className="hidden sm:inline">{status.label}</span>
            <span className="sm:hidden">{status.label.slice(0, 4)}</span>
          </Badge>
        );
      },
    },
    {
      id: "actions",
      header: "Actions",
      cell: (sale: any) => (
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setViewingSale(sale)}
          className="h-6 px-1 sm:h-8 sm:px-2"
        >
          <Eye className="h-3 w-3 sm:h-4 sm:w-4" />
          <span className="ml-1 hidden sm:inline text-xs">View</span>
        </Button>
      ),
    },
  ];

  // Profit calculation functions
  const calculateProfitForSale = (sale: Sale) => {
    let totalProfit = 0;

    sale.products?.forEach(item => {
      const product = productsData.find(p => p.id === item.productId);
      if (product) {
        // Use buyingPrice if available, otherwise estimate as 70% of selling price
        const costPrice = product.buyingPrice || (product.sellingPrice || product.price) * 0.7;
        const sellingPrice = item.price;
        const profit = (sellingPrice - costPrice) * item.quantity;
        totalProfit += profit;
      }
    });

    return totalProfit;
  };

  const totalSales = salesData?.reduce((sum: number, sale: any) => sum + (sale.amount || 0), 0) || 0;
  const totalProfit = salesData?.reduce((sum: number, sale: any) => sum + calculateProfitForSale(sale), 0) || 0;
  const averageTransaction = salesData?.length ? totalSales / salesData.length : 0;
  const averageProfit = salesData?.length ? totalProfit / salesData.length : 0;
  const totalTransactions = salesData?.length || 0;

  const getThisWeekSales = () => {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    return salesData?.reduce((sum: number, sale: any) => {
      const saleDate = new Date(sale.date || sale.timestamp);
      return saleDate >= startOfWeek ? sum + (sale.amount || 0) : sum;
    }, 0) || 0;
  };

  const getThisWeekProfit = () => {
    const now = new Date();
    const startOfWeek = new Date(now);
    startOfWeek.setDate(now.getDate() - now.getDay());
    startOfWeek.setHours(0, 0, 0, 0);

    return salesData?.reduce((sum: number, sale: any) => {
      const saleDate = new Date(sale.date || sale.timestamp);
      return saleDate >= startOfWeek ? sum + calculateProfitForSale(sale) : sum;
    }, 0) || 0;
  };

  const thisWeekSales = getThisWeekSales();
  const thisWeekProfit = getThisWeekProfit();

  return (
    <div className="space-y-4 sm:space-y-6 animate-fade-in">
      <div className="flex flex-col gap-4 sm:flex-row sm:justify-between sm:items-center">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold tracking-tight">Stationery Sales</h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Track and manage your stationery sales and customer transactions.
          </p>
        </div>
        <Dialog open={showSaleForm} onOpenChange={setShowSaleForm}>
          <DialogTrigger asChild>
            <Button className="w-full sm:w-auto">
              <Plus className="mr-2 h-4 w-4" />
              <span className="sm:hidden">Record Sale</span>
              <span className="hidden sm:inline">Record Sale</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[90vw] md:max-w-[700px] max-h-[90vh] overflow-y-auto">
            <RecordSaleForm
              onClose={() => setShowSaleForm(false)}
              onSaleRecorded={() => {
                refetchSales();
              }}
            />
          </DialogContent>
        </Dialog>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="transactions">Transactions</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            <MetricCard
              title="Total Sales"
              value={`Tsh${totalSales.toFixed(2)}`}
              description="All time sales value"
              icon={<DollarSign className="h-4 w-4" />}
              trend={{ value: 12.5, positive: true }}
            />
            <MetricCard
              title="Total Profit"
              value={`Tsh${totalProfit.toFixed(2)}`}
              description="All time profit earned"
              icon={<DollarSign className="h-4 w-4 text-green-600" />}
              trend={{ value: 15.2, positive: true }}
            />
            <MetricCard
              title="Sales This Week"
              value={`Tsh${thisWeekSales.toFixed(2)}`}
              description="This week's performance"
              icon={<BarChart className="h-4 w-4" />}
              trend={{ value: 4.2, positive: true }}
            />
            <MetricCard
              title="Profit This Week"
              value={`Tsh${thisWeekProfit.toFixed(2)}`}
              description="This week's profit"
              icon={<BarChart className="h-4 w-4 text-green-600" />}
              trend={{ value: 6.8, positive: true }}
            />
          </div>

          {/* Additional Profit Metrics */}
          <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
            <MetricCard
              title="Avg. Transaction"
              value={`Tsh${averageTransaction.toFixed(2)}`}
              description="Per sale average"
              icon={<ArrowUpRight className="h-4 w-4" />}
            />
            <MetricCard
              title="Avg. Profit"
              value={`Tsh${averageProfit.toFixed(2)}`}
              description="Per sale profit"
              icon={<ArrowUpRight className="h-4 w-4 text-green-600" />}
            />
            <MetricCard
              title="Total Transactions"
              value={totalTransactions}
              description="Number of sales"
              icon={<ShoppingCart className="h-4 w-4" />}
              trend={{ value: 2.1, positive: false }}
            />
            <MetricCard
              title="Profit Margin"
              value={`${totalSales > 0 ? ((totalProfit / totalSales) * 100).toFixed(1) : '0.0'}%`}
              description="Overall profit margin"
              icon={<Package className="h-4 w-4 text-green-600" />}
            />
          </div>
          
          <Card>
            <CardHeader className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <div>
                <CardTitle className="text-lg sm:text-xl">Recent Sales</CardTitle>
                <CardDescription>View your most recent sales transactions</CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowSaleForm(true)}
                className="w-full sm:w-auto"
              >
                <Plus className="mr-2 h-4 w-4" />
                <span className="sm:hidden">New Sale</span>
                <span className="hidden sm:inline">New Sale</span>
              </Button>
            </CardHeader>
            <CardContent>
              <DataTable
                data={salesData || []}
                columns={salesColumns}
                isLoading={isLoadingSales}
                emptyMessage="No sales records found"
              />
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="transactions" className="space-y-4">
          <Card>
            <CardHeader className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
              <div>
                <CardTitle className="text-lg sm:text-xl">All Transactions</CardTitle>
                <CardDescription>Complete list of all sales transactions</CardDescription>
              </div>
              <Button
                onClick={() => setShowSaleForm(true)}
                className="w-full sm:w-auto"
              >
                <Plus className="mr-2 h-4 w-4" />
                <span className="sm:hidden">Record Sale</span>
                <span className="hidden sm:inline">Record Sale</span>
              </Button>
            </CardHeader>
            <CardContent>
              <DataTable
                data={salesData || []}
                columns={salesColumns}
                isLoading={isLoadingSales}
                emptyMessage="No transactions available"
              />
            </CardContent>
          </Card>
        </TabsContent>
        

      </Tabs>

      {/* View Sold Products Dialog */}
      <Dialog open={!!viewingSale} onOpenChange={() => setViewingSale(null)}>
        <DialogContent className="sm:max-w-[600px] max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="text-lg sm:text-xl">Sale Details - Products Sold</DialogTitle>
          </DialogHeader>
          {viewingSale && (
            <div className="space-y-4">
              {/* Sale Summary */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 p-3 sm:p-4 bg-muted/50 rounded-lg">
                <div>
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">Sale Date</p>
                  <p className="text-xs sm:text-sm">{viewingSale.date}</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">Customer</p>
                  <p className="text-xs sm:text-sm">{viewingSale.customer}</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">Total Amount</p>
                  <p className="text-xs sm:text-sm font-medium">Tsh {viewingSale.amount?.toFixed(2) || '0.00'}</p>
                </div>
                <div>
                  <p className="text-xs sm:text-sm font-medium text-muted-foreground">Payment Method</p>
                  <p className="text-xs sm:text-sm capitalize">{viewingSale.paymentMethod || 'Cash'}</p>
                </div>
              </div>

              {/* Products List */}
              <div>
                <h4 className="text-sm sm:text-base font-medium mb-3 flex items-center">
                  <Package className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
                  Products Sold ({viewingSale.products?.length || 0} items)
                </h4>
                {viewingSale.products && viewingSale.products.length > 0 ? (
                  <div className="space-y-2">
                    {viewingSale.products.map((item: SaleItem, index: number) => (
                      <div key={index} className="flex flex-col sm:flex-row sm:items-center sm:justify-between p-2 sm:p-3 border rounded-lg gap-2">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm sm:text-base font-medium truncate">{item.productName}</p>
                          <p className="text-xs sm:text-sm text-muted-foreground">
                            Quantity: {item.quantity} × Tsh {item.price?.toFixed(2) || '0.00'}
                          </p>
                        </div>
                        <div className="text-left sm:text-right">
                          <p className="text-sm sm:text-base font-medium">
                            Tsh {((item.quantity || 0) * (item.price || 0)).toFixed(2)}
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-6 sm:py-8 text-muted-foreground">
                    <Package className="h-8 w-8 sm:h-12 sm:w-12 mx-auto mb-4 opacity-50" />
                    <p className="text-sm sm:text-base">No product details available for this sale</p>
                  </div>
                )}
              </div>

              {/* Summary */}
              {viewingSale.products && viewingSale.products.length > 0 && (
                <div className="border-t pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-sm sm:text-base font-medium">Total Items:</span>
                    <span className="text-sm sm:text-base">{viewingSale.products.reduce((sum: number, item: SaleItem) => sum + (item.quantity || 0), 0)}</span>
                  </div>
                  <div className="flex justify-between items-center mt-2">
                    <span className="text-sm sm:text-base font-medium">Total Amount:</span>
                    <span className="text-sm sm:text-base font-bold">Tsh {viewingSale.amount?.toFixed(2) || '0.00'}</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
