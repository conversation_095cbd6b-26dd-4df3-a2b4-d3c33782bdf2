
import { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle,
  CardFooter
} from "@/components/ui/card";
import { 
  Tabs, 
  Ta<PERSON><PERSON>ontent, 
  Ta<PERSON>List, 
  TabsTrigger 
} from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/context/AuthContext";
import { 
  User,
  Building, 
  Shield,
  Save,
  RefreshCw,
  Palette
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Toggle } from "@/components/ui/toggle";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { firestore } from "@/lib/firebase";

export default function Settings() {
  const [searchParams] = useSearchParams();
  const [activeTab, setActiveTab] = useState("profile");
  const { currentUser, isAdmin } = useAuth();
  const { toast } = useToast();
  const [isSaving, setIsSaving] = useState(false);
  const [userName, setUserName] = useState(currentUser?.displayName || "");
  const [theme, setTheme] = useState("light");

  // Handle URL tab parameter
  useEffect(() => {
    const tabParam = searchParams.get('tab');
    if (tabParam && ['profile', 'business', 'theme'].includes(tabParam)) {
      setActiveTab(tabParam);
    }
  }, [searchParams]);
  
  // Fetch current theme
  useEffect(() => {
    // Check if theme is stored in localStorage
    const storedTheme = localStorage.getItem("theme");
    if (storedTheme) {
      setTheme(storedTheme);
      document.documentElement.classList.toggle("dark", storedTheme === "dark");
    }
  }, []);

  const handleChangeTheme = (value: string) => {
    if (value) {
      setTheme(value);
      localStorage.setItem("theme", value);
      document.documentElement.classList.toggle("dark", value === "dark");
    }
  };

  const handleSaveProfile = () => {
    setIsSaving(true);
    
    // Update profile in Firebase
    if (currentUser) {
      firestore.collection("users").doc(currentUser.uid).update({
        displayName: userName
      })
      .then(() => {
        setIsSaving(false);
        toast({
          title: "Profile updated",
          description: "Your profile has been successfully updated.",
        });
      })
      .catch((error) => {
        console.error("Error updating profile:", error);
        setIsSaving(false);
        toast({
          title: "Update failed",
          description: "There was an error updating your profile.",
          variant: "destructive",
        });
      });
    }
  };

  return (
    <div className="h-full flex flex-col space-y-4 sm:space-y-6 animate-fade-in">
      <div className="flex flex-col gap-3 sm:gap-4 sm:flex-row sm:justify-between sm:items-center">
        <div>
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold tracking-tight">Settings</h1>
          <p className="text-muted-foreground text-xs sm:text-sm lg:text-base">
            Manage your application settings and preferences.
          </p>
        </div>
        {isAdmin() && (
          <Badge variant="secondary" className="px-2 py-1 text-xs sm:text-sm w-fit">
            Admin Access
          </Badge>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col space-y-3 sm:space-y-4">
        <TabsList className={`grid w-full ${isAdmin() ? 'grid-cols-3' : 'grid-cols-2'} h-auto`}>
          <TabsTrigger value="profile" className="text-xs sm:text-sm py-2 sm:py-3">
            <User className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden xs:inline sm:hidden">Profile</span>
            <span className="xs:hidden sm:inline">Profile</span>
            <span className="xs:hidden">P</span>
          </TabsTrigger>

          {isAdmin() && (
            <TabsTrigger value="business" className="text-xs sm:text-sm py-2 sm:py-3">
              <Building className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
              <span className="hidden xs:inline sm:hidden">Business</span>
              <span className="xs:hidden sm:inline">Business</span>
              <span className="xs:hidden">B</span>
            </TabsTrigger>
          )}

          <TabsTrigger value="theme" className="text-xs sm:text-sm py-2 sm:py-3">
            <Palette className="mr-1 sm:mr-2 h-3 w-3 sm:h-4 sm:w-4" />
            <span className="hidden xs:inline sm:hidden">Theme</span>
            <span className="xs:hidden sm:inline">Theme</span>
            <span className="xs:hidden">T</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="flex-1">
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Profile Settings</CardTitle>
              <CardDescription>Manage your personal information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name" className="text-sm sm:text-base">Full Name</Label>
                <Input
                  id="name"
                  value={userName}
                  onChange={(e) => setUserName(e.target.value)}
                  placeholder="Your name"
                  className="text-sm sm:text-base"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="email" className="text-sm sm:text-base">Email Address</Label>
                <Input
                  id="email"
                  value={currentUser?.email || ""}
                  placeholder="Your email"
                  type="email"
                  disabled
                  className="text-sm sm:text-base"
                />
                <p className="text-xs sm:text-sm text-muted-foreground">Email address cannot be changed</p>
              </div>
              <div className="space-y-2">
                <Label htmlFor="role" className="text-sm sm:text-base">Role</Label>
                <Input id="role" value={isAdmin() ? "Store Manager" : "Sales Associate"} disabled className="text-sm sm:text-base" />
                <p className="text-xs sm:text-sm text-muted-foreground">Your role determines your permissions in the stationery shop system</p>
              </div>
            </CardContent>
            <CardFooter className="flex-col sm:flex-row justify-end border-t px-4 sm:px-6 py-4 gap-2">
              <Button onClick={handleSaveProfile} disabled={isSaving} className="w-full sm:w-auto text-sm sm:text-base">
                {isSaving ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Save className="mr-2 h-4 w-4" />
                    <span className="sm:hidden">Save</span>
                    <span className="hidden sm:inline">Save Changes</span>
                  </>
                )}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {isAdmin() && (
          <TabsContent value="business" className="flex-1">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Store Settings</CardTitle>
                <CardDescription>Manage your stationery shop and business information</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="business-name" className="text-sm sm:text-base">Business Name</Label>
                  <Input id="business-name" defaultValue="Z&D Stationary" placeholder="Your stationery shop name" className="text-sm sm:text-base" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="business-address" className="text-sm sm:text-base">Store Address</Label>
                  <Textarea id="business-address" placeholder="Your stationery shop address" defaultValue="Dodoma City " className="text-sm sm:text-base resize-none" />
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="tax-id" className="text-sm sm:text-base">Tax ID / Registration Number</Label>
                    <Input id="tax-id" placeholder="Your tax ID" className="text-sm sm:text-base" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="currency" className="text-sm sm:text-base">Default Currency</Label>
                    <Input id="currency" defaultValue="TSH" disabled className="text-sm sm:text-base" />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="business-hours" className="text-sm sm:text-base">Store Hours</Label>
                  <Textarea
                    id="business-hours"
                    placeholder="Monday - Friday: 8AM - 7PM&#10;Saturday: 9AM - 6PM&#10;Sunday: 10AM - 4PM"
                    defaultValue="Monday - Friday: 8AM - 7PM&#10;Saturday: 9AM - 6PM&#10;Sunday: 10AM - 4PM"
                    className="text-sm sm:text-base resize-none"
                  />
                </div>
              </CardContent>
              <CardFooter className="flex-col sm:flex-row justify-end border-t px-4 sm:px-6 py-4 gap-2">
                <Button onClick={() => {
                  toast({
                    title: "Settings saved",
                    description: "Your business settings have been successfully updated.",
                  });
                }} className="w-full sm:w-auto text-sm sm:text-base">
                  <Save className="mr-2 h-4 w-4" />
                  <span className="sm:hidden">Save</span>
                  <span className="hidden sm:inline">Save Changes</span>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        )}

        <TabsContent value="theme" className="flex-1">
          <Card className="h-full">
            <CardHeader>
              <CardTitle className="text-lg sm:text-xl">Theme Settings</CardTitle>
              <CardDescription>Customize the application's appearance</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label className="text-sm sm:text-base">Color Theme</Label>
                <div className="flex flex-col space-y-4">
                  <ToggleGroup
                    type="single"
                    value={theme}
                    onValueChange={handleChangeTheme}
                    className="justify-start flex-col sm:flex-row gap-2"
                  >
                    <ToggleGroupItem value="light" className="flex gap-2 items-center w-full sm:w-auto text-sm sm:text-base">
                      <div className="w-4 h-4 sm:w-5 sm:h-5 rounded-full bg-white border border-gray-300"></div>
                      Light
                    </ToggleGroupItem>
                    <ToggleGroupItem value="dark" className="flex gap-2 items-center w-full sm:w-auto text-sm sm:text-base">
                      <div className="w-4 h-4 sm:w-5 sm:h-5 rounded-full bg-gray-800 border border-gray-600"></div>
                      Dark
                    </ToggleGroupItem>
                  </ToggleGroup>

                  <div className="pt-4">
                    <p className="text-xs sm:text-sm font-medium mb-2">Preview</p>
                    <div className={`p-3 sm:p-4 rounded-md border ${theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-white text-black'}`}>
                      <p className="font-medium text-sm sm:text-base">This is how your application will look</p>
                      <p className="text-xs sm:text-sm mt-1 opacity-80">Sample text with {theme} theme applied</p>
                    </div>
                  </div>
                </div>
              </div>

              <div className="space-y-2 pt-4 border-t">
                <Label className="text-sm sm:text-base">Sidebar Behavior</Label>
                <div className="flex items-center gap-2">
                  <Toggle className="text-xs sm:text-sm">
                    Auto-hide sidebar on small screens
                  </Toggle>
                </div>
              </div>
            </CardContent>
            <CardFooter className="flex-col sm:flex-row justify-end border-t px-4 sm:px-6 py-4 gap-2">
              <Button onClick={() => {
                // Already saved to localStorage in handleChangeTheme
                toast({
                  title: "Theme updated",
                  description: `The application theme has been set to ${theme}.`,
                });
              }} className="w-full sm:w-auto text-sm sm:text-base">
                <Save className="mr-2 h-4 w-4" />
                <span className="sm:hidden">Save</span>
                <span className="hidden sm:inline">Save Theme</span>
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
        
        {isAdmin() && (
          <TabsContent value="security" className="flex-1">
            <Card className="h-full">
              <CardHeader>
                <CardTitle className="text-lg sm:text-xl">Security Settings</CardTitle>
                <CardDescription>Manage your security and privacy settings</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="current-password" className="text-sm sm:text-base">Current Password</Label>
                  <Input id="current-password" type="password" className="text-sm sm:text-base" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="new-password" className="text-sm sm:text-base">New Password</Label>
                  <Input id="new-password" type="password" className="text-sm sm:text-base" />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="confirm-password" className="text-sm sm:text-base">Confirm New Password</Label>
                  <Input id="confirm-password" type="password" className="text-sm sm:text-base" />
                </div>


              </CardContent>
              <CardFooter className="flex-col sm:flex-row justify-end border-t px-4 sm:px-6 py-4 gap-2">
                <Button onClick={() => {
                  toast({
                    title: "Security settings updated",
                    description: "Your security settings have been successfully updated.",
                  });
                }} className="w-full sm:w-auto text-sm sm:text-base">
                  <Save className="mr-2 h-4 w-4" />
                  <span className="sm:hidden">Update</span>
                  <span className="hidden sm:inline">Update Security Settings</span>
                </Button>
              </CardFooter>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}
