
import { useState } from "react";
import { 
  <PERSON><PERSON><PERSON> as <PERSON><PERSON>hart<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON> as <PERSON>Chart<PERSON><PERSON>, 
  <PERSON><PERSON><PERSON> as <PERSON><PERSON><PERSON>Icon, 
  Activity, 
  TrendingUp, 
  TrendingDown, 
  Users,
  Calendar,
  Download
} from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { MetricCard } from "@/components/common/MetricCard";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>atter,
  ScatterChart,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  Cell,
  ResponsiveContainer,
} from "recharts";

// Mock data for sales trend
const salesTrendData = [
  { month: "Jan", value: 12000 },
  { month: "Feb", value: 19000 },
  { month: "Mar", value: 15000 },
  { month: "Apr", value: 22000 },
  { month: "May", value: 28000 },
  { month: "Jun", value: 32000 },
  { month: "Jul", value: 25000 },
  { month: "Aug", value: 29000 },
  { month: "Sep", value: 36000 },
  { month: "Oct", value: 31000 },
  { month: "Nov", value: 38000 },
  { month: "Dec", value: 42000 },
];

// Mock data for customer demographics
const customerData = [
  { name: "18-24", value: 15 },
  { name: "25-34", value: 30 },
  { name: "35-44", value: 25 },
  { name: "45-54", value: 18 },
  { name: "55+", value: 12 },
];

// Mock data for product performance
const productPerformanceData = [
  { name: "Product A", sales: 4000, returns: 240 },
  { name: "Product B", sales: 3000, returns: 120 },
  { name: "Product C", sales: 2000, returns: 80 },
  { name: "Product D", sales: 2780, returns: 100 },
  { name: "Product E", sales: 1890, returns: 230 },
  { name: "Product F", sales: 2390, returns: 150 },
  { name: "Product G", sales: 3490, returns: 210 },
];

// Mock data for scatter plot (price vs. sales)
const priceSalesData = [
  { x: 50, y: 240, name: "Product A" },
  { x: 120, y: 180, name: "Product B" },
  { x: 150, y: 120, name: "Product C" },
  { x: 180, y: 140, name: "Product D" },
  { x: 220, y: 90, name: "Product E" },
  { x: 250, y: 80, name: "Product F" },
  { x: 280, y: 60, name: "Product G" },
  { x: 300, y: 100, name: "Product H" },
  { x: 350, y: 50, name: "Product I" },
  { x: 400, y: 40, name: "Product J" },
  { x: 90, y: 220, name: "Product K" },
  { x: 140, y: 160, name: "Product L" },
  { x: 200, y: 110, name: "Product M" },
];

// Colors for pie chart
const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

// Time periods
const timePeriods = [
  "Last 7 Days",
  "Last 30 Days",
  "This Month",
  "Last Month",
  "This Quarter",
  "Last Quarter",
  "This Year",
  "Last Year",
  "Custom Range"
];

export default function AnalyticsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("This Month");

  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Advanced Analytics</h1>
          <p className="text-muted-foreground">
            Gain insights into your business performance with advanced analytics.
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select Period" />
            </SelectTrigger>
            <SelectContent>
              {timePeriods.map((period) => (
                <SelectItem key={period} value={period}>{period}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button variant="outline" size="icon">
            <Calendar className="h-4 w-4" />
          </Button>
          <Button variant="outline" size="icon">
            <Download className="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <MetricCard
          title="Total Revenue"
          value="Tsh 156,234"
          description="vs. previous period"
          icon={<BarChartIcon className="h-4 w-4" />}
          trend={{ value: 12.5, positive: true }}
        />
        <MetricCard
          title="Conversion Rate"
          value="3.2%"
          description="vs. previous period"
          icon={<Activity className="h-4 w-4" />}
          trend={{ value: 0.5, positive: true }}
        />
        <MetricCard
          title="Avg. Order Value"
          value="Tsh 85.25"
          description="vs. previous period"
          icon={<TrendingUp className="h-4 w-4" />}
          trend={{ value: 2.1, positive: true }}
        />
        <MetricCard
          title="Customer Churn"
          value="1.2%"
          description="vs. previous period"
          icon={<TrendingDown className="h-4 w-4" />}
          trend={{ value: 0.3, positive: false }}
        />
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Revenue Trend</CardTitle>
            <CardDescription>Sales performance over the last 12 months</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={salesTrendData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis dataKey="month" />
                  <YAxis />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                      border: 'none',
                    }}
                    formatter={(value) => [`$${value}`, 'Revenue']}
                  />
                  <Legend />
                  <Line 
                    type="monotone" 
                    dataKey="value" 
                    name="Revenue" 
                    stroke="hsl(var(--primary))" 
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Product Performance</CardTitle>
            <CardDescription>Sales vs. Returns by product</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={productPerformanceData}
                  margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip
                    contentStyle={{
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                      border: 'none',
                    }}
                  />
                  <Legend />
                  <Bar dataKey="sales" name="Sales" fill="hsl(var(--primary))" radius={[4, 4, 0, 0]} />
                  <Bar dataKey="returns" name="Returns" fill="hsl(var(--destructive))" radius={[4, 4, 0, 0]} />
                </BarChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Customer Demographics</CardTitle>
            <CardDescription>Age distribution of customers</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={customerData}
                    cx="50%"
                    cy="50%"
                    labelLine={true}
                    outerRadius={120}
                    fill="#8884d8"
                    dataKey="value"
                    label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  >
                    {customerData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip
                    formatter={(value) => [`${value}%`, 'Percentage']}
                    contentStyle={{
                      backgroundColor: 'white',
                      borderRadius: '8px',
                      boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                      border: 'none',
                    }}
                  />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Price vs. Sales Analysis</CardTitle>
            <CardDescription>Impact of pricing on product sales</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="h-[350px]">
              <ResponsiveContainer width="100%" height="100%">
                <ScatterChart
                  margin={{ top: 20, right: 30, left: 20, bottom: 20 }}
                >
                  <CartesianGrid strokeDasharray="3 3" opacity={0.2} />
                  <XAxis type="number" dataKey="x" name="Price" unit="$" />
                  <YAxis type="number" dataKey="y" name="Units Sold" />
                  <Tooltip 
                    cursor={{ strokeDasharray: '3 3' }}
                    content={({ active, payload }) => {
                      if (active && payload && payload.length) {
                        return (
                          <div className="bg-white p-2 border rounded shadow-sm">
                            <p className="font-semibold">{payload[0].payload.name}</p>
                            <p>Price: ${payload[0].value}</p>
                            <p>Sales: {payload[1].value} units</p>
                          </div>
                        );
                      }
                      return null;
                    }}
                  />
                  <Scatter name="Products" data={priceSalesData} fill="hsl(var(--primary))" />
                </ScatterChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Customer Insights</CardTitle>
          <CardDescription>Understand your customer behavior and patterns</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-3">
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Acquisition Channels</h3>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm">Social Media</span>
                  <span className="text-sm font-medium">42%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-500 h-2 rounded-full" style={{ width: "42%" }}></div>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm">Direct Search</span>
                  <span className="text-sm font-medium">28%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-500 h-2 rounded-full" style={{ width: "28%" }}></div>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm">Email Marketing</span>
                  <span className="text-sm font-medium">15%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-yellow-500 h-2 rounded-full" style={{ width: "15%" }}></div>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm">Referrals</span>
                  <span className="text-sm font-medium">10%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-orange-500 h-2 rounded-full" style={{ width: "10%" }}></div>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm">Other</span>
                  <span className="text-sm font-medium">5%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-500 h-2 rounded-full" style={{ width: "5%" }}></div>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Customer Retention</h3>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm">First-time</span>
                  <span className="text-sm font-medium">45%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-primary h-2 rounded-full" style={{ width: "45%" }}></div>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm">Returning (2-5 purchases)</span>
                  <span className="text-sm font-medium">30%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-primary/80 h-2 rounded-full" style={{ width: "30%" }}></div>
                </div>
              </div>
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm">Loyal (6+ purchases)</span>
                  <span className="text-sm font-medium">25%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-primary/60 h-2 rounded-full" style={{ width: "25%" }}></div>
                </div>
              </div>
              <div className="mt-4 pt-2 border-t">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">Retention Rate</span>
                  <span className="text-sm font-bold text-green-500">62%</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-2">
              <h3 className="text-lg font-medium">Geographic Distribution</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">North America</span>
                  <span className="text-sm font-medium">65%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Europe</span>
                  <span className="text-sm font-medium">20%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Asia Pacific</span>
                  <span className="text-sm font-medium">10%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Latin America</span>
                  <span className="text-sm font-medium">3%</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Other Regions</span>
                  <span className="text-sm font-medium">2%</span>
                </div>
              </div>
              <div className="mt-4 pt-2 border-t">
                <Button variant="outline" size="sm" className="w-full">
                  <Users className="mr-2 h-4 w-4" />
                  View Detailed Demographics
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
        <CardFooter className="border-t">
          <Button className="w-full">
            <Download className="mr-2 h-4 w-4" />
            Export Analytics Report
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
