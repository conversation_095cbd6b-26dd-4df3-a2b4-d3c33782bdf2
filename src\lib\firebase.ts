import { initializeApp } from 'firebase/app';
import { 
  getAuth, 
  signInWithEmailAndPassword, 
  signOut as firebaseSignOut,
  onAuthStateChanged
} from 'firebase/auth';
import { 
  getFirestore, 
  collection, 
  doc, 
  getDoc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  onSnapshot,
  query,
  where,
  serverTimestamp,
  DocumentData
} from 'firebase/firestore';
import type { Product, FirestoreUser, Expense } from '@/types/firebase';

// Firebase configuration
export const firebaseConfig = {
  apiKey: "AIzaSyDvwbB4WUw_ehVzqFE1N0fhLlRfbfgUpjg",
  authDomain: "stationary-e71dc.firebaseapp.com",
  projectId: "stationary-e71dc",
  storageBucket: "stationary-e71dc.firebasestorage.app",
  messagingSenderId: "711863718942",
  appId: "1:711863718942:web:52df9e6dca61435ca9ca0f",
  measurementId: "G-FE32BTXR50"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
const firebaseAuth = getAuth(app);
export const firebaseFirestore = getFirestore(app); // Added export

// Auth service
export const auth = {
  currentUser: firebaseAuth.currentUser,
  async signIn(email: string, password: string) {
    try {
      const userCredential = await signInWithEmailAndPassword(firebaseAuth, email, password);
      const userDoc = await getDoc(doc(firebaseFirestore, 'users', userCredential.user.uid));
      const userData = userDoc.exists() ? userDoc.data() as FirestoreUser : null;
      
      return { 
        user: { 
          uid: userCredential.user.uid, 
          email: userCredential.user.email, 
          displayName: userCredential.user.displayName || userData?.displayName || email.split('@')[0],
          role: userData?.role || 'shopkeeper'
        } 
      };
    } catch (error) {
      console.error("Error signing in:", error);
      throw error;
    }
  },
  async signOut() {
    try {
      await firebaseSignOut(firebaseAuth);
      return true;
    } catch (error) {
      console.error("Error signing out:", error);
      throw error;
    }
  },
  onAuthStateChanged,
};

// Firestore service
export const firestore = {
  collection: (name: string) => ({
    get: async () => {
      try {
        const querySnapshot = await getDocs(collection(firebaseFirestore, name));
        return {
          docs: querySnapshot.docs.map(doc => ({
            id: doc.id,
            ...doc.data()
          })),
        };
      } catch (error) {
        console.error(`Error getting collection ${name}:`, error);
        throw error;
      }
    },
    add: async (data: DocumentData) => {
      try {
        const docRef = await addDoc(collection(firebaseFirestore, name), {
          ...data,
          createdAt: serverTimestamp(),
        });
        return { id: docRef.id };
      } catch (error) {
        console.error(`Error adding document to ${name}:`, error);
        throw error;
      }
    },
    doc: (id: string) => ({
      get: async () => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          const docSnap = await getDoc(docRef);
          return {
            exists: docSnap.exists(),
            data: () => ({ id: docSnap.id, ...docSnap.data() }),
          };
        } catch (error) {
          console.error(`Error getting document ${name}/${id}:`, error);
          throw error;
        }
      },
      update: async (data: DocumentData) => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          await updateDoc(docRef, data);
          return true;
        } catch (error) {
          console.error(`Error updating document ${name}/${id}:`, error);
          throw error;
        }
      },
      delete: async () => {
        try {
          const docRef = doc(firebaseFirestore, name, id);
          await deleteDoc(docRef);
          return true;
        } catch (error) {
          console.error(`Error deleting document ${name}/${id}:`, error);
          throw error;
        }
      },
    }),
    onSnapshot: (callback: (data: DocumentData[]) => void) => {
      const q = collection(firebaseFirestore, name);
      return onSnapshot(q, (querySnapshot) => {
        const items = querySnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        }));
        callback(items);
      });
    },
    where: (field: string, operator: any, value: any) => {
      const q = query(
        collection(firebaseFirestore, name),
        where(field, operator, value)
      );
      return {
        get: async () => {
          try {
            const querySnapshot = await getDocs(q);
            return {
              docs: querySnapshot.docs.map(doc => ({
                id: doc.id,
                ...doc.data()
              })),
            };
          } catch (error) {
            console.error(`Error querying collection ${name}:`, error);
            throw error;
          }
        },
        onSnapshot: (callback: (data: DocumentData[]) => void) => {
          return onSnapshot(q, (querySnapshot) => {
            const items = querySnapshot.docs.map(doc => ({
              id: doc.id,
              ...doc.data()
            }));
            callback(items);
          });
        }
      };
    }
  }),
};

// Expense management functions
export const expenseService = {
  // Get all expenses
  getAll: async () => {
    try {
      const querySnapshot = await getDocs(collection(firebaseFirestore, 'expenses'));
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Expense[];
    } catch (error) {
      console.error('Error getting expenses:', error);
      throw error;
    }
  },

  // Get expenses by user
  getByUser: async (userId: string) => {
    try {
      const q = query(
        collection(firebaseFirestore, 'expenses'),
        where('createdBy', '==', userId)
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Expense[];
    } catch (error) {
      console.error('Error getting user expenses:', error);
      throw error;
    }
  },

  // Get expenses by status
  getByStatus: async (status: 'pending' | 'approved' | 'rejected') => {
    try {
      const q = query(
        collection(firebaseFirestore, 'expenses'),
        where('status', '==', status)
      );
      const querySnapshot = await getDocs(q);
      return querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Expense[];
    } catch (error) {
      console.error('Error getting expenses by status:', error);
      throw error;
    }
  },

  // Add new expense
  add: async (expenseData: Omit<Expense, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      const docRef = await addDoc(collection(firebaseFirestore, 'expenses'), {
        ...expenseData,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      return { id: docRef.id };
    } catch (error) {
      console.error('Error adding expense:', error);
      throw error;
    }
  },

  // Update expense
  update: async (id: string, expenseData: Partial<Expense>) => {
    try {
      const docRef = doc(firebaseFirestore, 'expenses', id);
      await updateDoc(docRef, {
        ...expenseData,
        updatedAt: serverTimestamp(),
      });
      return true;
    } catch (error) {
      console.error('Error updating expense:', error);
      throw error;
    }
  },

  // Delete expense
  delete: async (id: string) => {
    try {
      const docRef = doc(firebaseFirestore, 'expenses', id);
      await deleteDoc(docRef);
      return true;
    } catch (error) {
      console.error('Error deleting expense:', error);
      throw error;
    }
  },

  // Get expense by ID
  getById: async (id: string) => {
    try {
      const docRef = doc(firebaseFirestore, 'expenses', id);
      const docSnap = await getDoc(docRef);
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as Expense;
      }
      return null;
    } catch (error) {
      console.error('Error getting expense by ID:', error);
      throw error;
    }
  },

  // Calculate total expenses
  calculateTotal: async (filters?: {
    startDate?: string;
    endDate?: string;
    category?: string;
    status?: string;
  }) => {
    try {
      let q = collection(firebaseFirestore, 'expenses');

      if (filters?.status) {
        q = query(q, where('status', '==', filters.status)) as any;
      }

      if (filters?.category) {
        q = query(q, where('category', '==', filters.category)) as any;
      }

      const querySnapshot = await getDocs(q);
      const expenses = querySnapshot.docs.map(doc => doc.data()) as Expense[];

      // Filter by date range if provided
      let filteredExpenses = expenses;
      if (filters?.startDate || filters?.endDate) {
        filteredExpenses = expenses.filter(expense => {
          const expenseDate = new Date(expense.date);
          if (filters.startDate && expenseDate < new Date(filters.startDate)) return false;
          if (filters.endDate && expenseDate > new Date(filters.endDate)) return false;
          return true;
        });
      }

      return filteredExpenses.reduce((total, expense) => total + expense.amount, 0);
    } catch (error) {
      console.error('Error calculating total expenses:', error);
      throw error;
    }
  },

  // Get expense categories with totals
  getCategorySummary: async () => {
    try {
      const querySnapshot = await getDocs(collection(firebaseFirestore, 'expenses'));
      const expenses = querySnapshot.docs.map(doc => doc.data()) as Expense[];

      const categorySummary: { [key: string]: { total: number; count: number } } = {};

      expenses.forEach(expense => {
        if (!categorySummary[expense.category]) {
          categorySummary[expense.category] = { total: 0, count: 0 };
        }
        categorySummary[expense.category].total += expense.amount;
        categorySummary[expense.category].count += 1;
      });

      return categorySummary;
    } catch (error) {
      console.error('Error getting category summary:', error);
      throw error;
    }
  }
};

// Create demo users in Firebase
export const createDemoData = async () => {
  try {
    // Check if we already have products
    const productsSnapshot = await getDocs(collection(firebaseFirestore, 'products'));
    if (productsSnapshot.empty) {
      const demoProducts = [
        { name: 'BIC Ballpoint Pen Blue', price: 1.25, stock: 50, category: 'Pens & Pencils', sku: 'PEN001', description: 'Classic blue ballpoint pen, smooth writing, reliable ink flow', threshold: 10 },
        { name: 'A4 Copy Paper 500 Sheets', price: 8.99, stock: 25, category: 'Paper Products', sku: 'PAP001', description: 'High-quality white A4 copy paper, 80gsm weight, perfect for printing and copying', threshold: 5 },
        { name: 'Stapler Heavy Duty', price: 15.99, stock: 8, category: 'Office Supplies', sku: 'OFF001', description: 'Heavy-duty stapler, staples up to 50 sheets, includes 1000 staples', threshold: 3 },
        { name: 'Colored Pencils Set 24pc', price: 12.50, stock: 15, category: 'Art Supplies', sku: 'ART001', description: 'Professional colored pencils set, vibrant colors, perfect for drawing and coloring', threshold: 5 },
        { name: 'Spiral Notebook A5', price: 3.75, stock: 30, category: 'Notebooks & Journals', sku: 'NOT001', description: 'A5 spiral-bound notebook, 200 pages, ruled lines, durable cover', threshold: 8 },
        { name: 'Highlighter Set 4 Colors', price: 6.99, stock: 20, category: 'Pens & Pencils', sku: 'PEN002', description: 'Fluorescent highlighter set in yellow, pink, green, and blue', threshold: 6 },
        { name: 'File Folders Letter Size', price: 4.25, stock: 12, category: 'Filing & Organization', sku: 'FIL001', description: 'Manila file folders, letter size, pack of 25, perfect for document organization', threshold: 4 },
        { name: 'Desk Organizer Bamboo', price: 22.99, stock: 6, category: 'Desk Accessories', sku: 'DSK001', description: 'Eco-friendly bamboo desk organizer with multiple compartments for pens, clips, and supplies', threshold: 2 },
        { name: 'Sticky Notes 3x3 Yellow', price: 2.99, stock: 40, category: 'Stickers & Labels', sku: 'STK001', description: 'Classic yellow sticky notes, 3x3 inches, pack of 100 sheets, strong adhesive', threshold: 10 },
        { name: 'Correction Tape', price: 3.50, stock: 0, category: 'Office Supplies', sku: 'OFF002', description: 'White correction tape, 5mm width, easy application, clean coverage', threshold: 8 },
      ];
      
      for (const product of demoProducts) {
        await addDoc(collection(firebaseFirestore, 'products'), {
          ...product,
          createdAt: serverTimestamp(),
        });
      }
      console.log('Demo products created');
    }

    // Check if we already have users
    const usersSnapshot = await getDocs(collection(firebaseFirestore, 'users'));
    if (usersSnapshot.empty) {
      const demoUsers = [
        { email: '<EMAIL>', displayName: 'Store Manager', role: 'admin' },
        { email: '<EMAIL>', displayName: 'Sales Associate', role: 'shopkeeper' },
      ];
      
      for (const user of demoUsers) {
        await addDoc(collection(firebaseFirestore, 'users'), {
          ...user,
          createdAt: serverTimestamp(),
        });
      }
      console.log('Demo user documents created');
    }

    // Check if we already have expenses
    const expensesSnapshot = await getDocs(collection(firebaseFirestore, 'expenses'));
    if (expensesSnapshot.empty) {
      const demoExpenses = [
        {
          title: 'Office Rent',
          description: 'Monthly office rent payment',
          amount: 1200.00,
          category: 'Rent & Utilities',
          date: new Date().toISOString().split('T')[0],
          paymentMethod: 'bank_transfer' as const,
          vendor: 'Property Management Co.',
          receiptNumber: 'RENT-001',
          isRecurring: true,
          recurringFrequency: 'monthly' as const,
          tags: ['rent', 'monthly', 'fixed'],
          createdBy: '<EMAIL>',
          status: 'approved' as const
        },
        {
          title: 'Electricity Bill',
          description: 'Monthly electricity bill',
          amount: 150.75,
          category: 'Rent & Utilities',
          date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          paymentMethod: 'card' as const,
          vendor: 'Electric Company',
          receiptNumber: 'ELEC-001',
          isRecurring: true,
          recurringFrequency: 'monthly' as const,
          tags: ['utilities', 'electricity'],
          createdBy: '<EMAIL>',
          status: 'approved' as const
        },
        {
          title: 'Office Supplies Purchase',
          description: 'Bulk purchase of office supplies',
          amount: 350.25,
          category: 'Inventory',
          date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          paymentMethod: 'card' as const,
          vendor: 'Office Depot',
          receiptNumber: 'INV-001',
          isRecurring: false,
          tags: ['inventory', 'supplies'],
          createdBy: '<EMAIL>',
          status: 'approved' as const
        },
        {
          title: 'Marketing Campaign',
          description: 'Social media advertising campaign',
          amount: 200.00,
          category: 'Marketing',
          date: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          paymentMethod: 'card' as const,
          vendor: 'Facebook Ads',
          receiptNumber: 'MKT-001',
          isRecurring: false,
          tags: ['marketing', 'advertising', 'social media'],
          createdBy: '<EMAIL>',
          status: 'approved' as const
        },
        {
          title: 'Equipment Maintenance',
          description: 'Printer and computer maintenance',
          amount: 125.50,
          category: 'Maintenance',
          date: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
          paymentMethod: 'cash' as const,
          vendor: 'Tech Support Co.',
          receiptNumber: 'MAINT-001',
          isRecurring: false,
          tags: ['maintenance', 'equipment'],
          createdBy: '<EMAIL>',
          status: 'pending' as const
        }
      ];

      for (const expense of demoExpenses) {
        await addDoc(collection(firebaseFirestore, 'expenses'), {
          ...expense,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp(),
        });
      }
      console.log('Demo expenses created');
    }
  } catch (error) {
    console.error('Error creating demo data:', error);
  }
};

// Initialize demo data
createDemoData();

export default { auth, firestore, expenseService, createDemoData };